<template>
  <view class="advanced-chart-container">
    <!-- 简化的图表头部 -->
    <view class="chart-header-simple">
      <view class="chart-icon-wrapper">
        <svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2.5" stroke-linecap="round" stroke-linejoin="round">
          <path d="M22 12h-4l-3 9L9 3l-3 9H2"></path>
        </svg>
      </view>
      <view class="chart-title-section">
        <text class="chart-title-modern">{{ title }}</text>
        <text class="chart-subtitle-modern">{{ subtitle }}</text>
      </view>
    </view>
    
    <!-- 图表容器 -->
    <view class="chart-canvas-container">
      <canvas
        :canvas-id="canvasId"
        class="chart-canvas"
        :style="{ width: chartWidth + 'rpx', height: chartHeight + 'rpx' }"
      ></canvas>
      
      <!-- 误差带指示器 -->
      <view class="error-band-indicator" v-if="showErrorBand">
        <view class="indicator-item">
          <view class="indicator-color" style="background: rgba(59, 130, 246, 0.2);"></view>
          <text class="indicator-text">±5% 误差带</text>
        </view>
      </view>
    </view>
    
    <!-- 数据统计 -->
    <view class="chart-statistics">
      <view class="stat-item">
        <text class="stat-label">平均值</text>
        <text class="stat-value">{{ statistics.average }}</text>
      </view>
      <view class="stat-item">
        <text class="stat-label">最大值</text>
        <text class="stat-value">{{ statistics.max }}</text>
      </view>
      <view class="stat-item">
        <text class="stat-label">最小值</text>
        <text class="stat-value">{{ statistics.min }}</text>
      </view>
      <view class="stat-item">
        <text class="stat-label">数据点</text>
        <text class="stat-value">{{ statistics.count }}</text>
      </view>
    </view>
  </view>
</template>

<script>
import { ref, computed, onMounted, watch } from 'vue'

export default {
  name: 'AdvancedChart',
  props: {
    title: {
      type: String,
      default: '数据趋势'
    },
    subtitle: {
      type: String,
      default: '实时监测数据'
    },
    data: {
      type: Array,
      default: () => []
    },
    canvasId: {
      type: String,
      required: true
    },
    chartWidth: {
      type: Number,
      default: 350
    },
    chartHeight: {
      type: Number,
      default: 200
    },
    showErrorBand: {
      type: Boolean,
      default: true
    }
  },
  
  setup(props) {
    const chartContext = ref(null)
    const selectedPeriod = ref('1h')

    const timePeriods = ref([
      { label: '1小时', value: '1h' },
      { label: '6小时', value: '6h' },
      { label: '24小时', value: '24h' },
      { label: '7天', value: '7d' }
    ])
    
    // 计算统计数据
    const statistics = computed(() => {
      if (!props.data || props.data.length === 0) {
        return { average: '0.00', max: '0.00', min: '0.00', count: 0 }
      }
      
      const values = props.data.map(item => item.value || item.doseRate || 0)
      const sum = values.reduce((a, b) => a + b, 0)
      const avg = sum / values.length
      const max = Math.max(...values)
      const min = Math.min(...values)
      
      return {
        average: avg.toFixed(2),
        max: max.toFixed(2),
        min: min.toFixed(2),
        count: values.length
      }
    })
    
    // 选择时间段
    const selectPeriod = (period) => {
      selectedPeriod.value = period
      drawChart()
    }
    
    // 初始化图表
    const initChart = () => {
      const ctx = uni.createCanvasContext(props.canvasId)
      if (!ctx) return
      
      chartContext.value = ctx
      drawChart()
    }
    
    // 绘制图表
    const drawChart = () => {
      if (!chartContext.value) return

      const ctx = chartContext.value
      // 将rpx转换为实际像素，小程序中1rpx ≈ 0.5px
      const width = props.chartWidth * 0.5
      const height = props.chartHeight * 0.5
      const padding = 20  // 减小padding以适应小屏幕
      const chartWidth = width - padding * 2
      const chartHeight = height - padding * 2
      
      // 清空画布
      ctx.clearRect(0, 0, width, height)
      
      // 获取数据
      const data = getFilteredData()
      if (data.length === 0) return
      
      const values = data.map(item => item.value || item.doseRate || 0)
      const maxValue = Math.max(...values)
      const minValue = Math.min(...values)
      const range = maxValue - minValue || 1
      
      // 绘制背景网格
      drawGrid(ctx, width, height, padding)
      
      // 绘制误差带
      if (props.showErrorBand) {
        drawErrorBand(ctx, data, width, height, padding, minValue, range)
      }

      // 绘制折线图
      drawLineChart(ctx, data, width, height, padding, minValue, range)
      
      // 绘制坐标轴
      drawAxes(ctx, width, height, padding)
      
      ctx.draw()
    }
    
    // 获取过滤后的数据
    const getFilteredData = () => {
      if (!props.data || props.data.length === 0) return []
      
      const now = new Date()
      let startTime
      
      switch (selectedPeriod.value) {
        case '1h':
          startTime = new Date(now.getTime() - 60 * 60 * 1000)
          break
        case '6h':
          startTime = new Date(now.getTime() - 6 * 60 * 60 * 1000)
          break
        case '24h':
          startTime = new Date(now.getTime() - 24 * 60 * 60 * 1000)
          break
        case '7d':
          startTime = new Date(now.getTime() - 7 * 24 * 60 * 60 * 1000)
          break
        default:
          return props.data.slice(0, 25) // 减半默认数据点数
      }
      
      return props.data.filter(item => {
        const itemTime = new Date(item.timestamp || item.time || now)
        return itemTime >= startTime
      }).slice(0, 50) // 减半最大数据点数
    }

    // 平滑滤波函数 - 直接连接数据点，使用移动平均平滑
    const createSmoothPoints = (points) => {
      if (points.length < 3) return points

      // 应用移动平均滤波，直接平滑原始数据点
      return applyMovingAverage(points, 3)
    }

    // 移动平均滤波 - 保持原始X坐标，只平滑Y值
    const applyMovingAverage = (points, windowSize) => {
      if (points.length <= windowSize) return points

      const smoothed = []
      const halfWindow = Math.floor(windowSize / 2)

      for (let i = 0; i < points.length; i++) {
        let sumY = 0, count = 0

        const start = Math.max(0, i - halfWindow)
        const end = Math.min(points.length - 1, i + halfWindow)

        for (let j = start; j <= end; j++) {
          sumY += points[j].y
          count++
        }

        // 保持原始X坐标，只平滑Y坐标
        smoothed.push({
          x: points[i].x,
          y: sumY / count
        })
      }

      return smoothed
    }

    // 绘制网格 - 参考Chart.js的网格样式
    const drawGrid = (ctx, width, height, padding) => {
      // 主网格线
      ctx.setStrokeStyle('rgba(0, 0, 0, 0.08)')
      ctx.setLineWidth(0.5)

      // 水平网格线
      for (let i = 1; i < 5; i++) {
        const y = padding + (i * (height - padding * 2) / 5)
        ctx.beginPath()
        ctx.moveTo(padding, y)
        ctx.lineTo(width - padding, y)
        ctx.stroke()
      }

      // 垂直网格线
      for (let i = 1; i < 6; i++) {
        const x = padding + (i * (width - padding * 2) / 6)
        ctx.beginPath()
        ctx.moveTo(x, padding)
        ctx.lineTo(x, height - padding)
        ctx.stroke()
      }

      // 添加更细的辅助网格线
      ctx.setStrokeStyle('rgba(0, 0, 0, 0.03)')
      ctx.setLineWidth(0.25)

      // 水平辅助网格线
      for (let i = 0.5; i < 5; i += 1) {
        const y = padding + (i * (height - padding * 2) / 5)
        ctx.beginPath()
        ctx.moveTo(padding, y)
        ctx.lineTo(width - padding, y)
        ctx.stroke()
      }

      // 垂直辅助网格线
      for (let i = 0.5; i < 6; i += 1) {
        const x = padding + (i * (width - padding * 2) / 6)
        ctx.beginPath()
        ctx.moveTo(x, padding)
        ctx.lineTo(x, height - padding)
        ctx.stroke()
      }
    }
    
    // 绘制误差带 - 参考Chart.js的误差带样式
    const drawErrorBand = (ctx, data, width, height, padding, minValue, range) => {
      if (data.length < 2) return

      const chartWidth = width - padding * 2
      const chartHeight = height - padding * 2

      // 计算平均值
      const values = data.map(item => item.value || item.doseRate || 0)
      const average = values.reduce((a, b) => a + b, 0) / values.length

      // 5% 误差带
      const errorPercent = 0.05
      const upperBound = average * (1 + errorPercent)
      const lowerBound = average * (1 - errorPercent)

      // 绘制误差带填充
      ctx.beginPath()
      ctx.moveTo(padding, height - padding - ((lowerBound - minValue) / range) * chartHeight)

      // 上边界线 - 使用平滑曲线
      for (let i = 0; i < data.length; i++) {
        const x = padding + (i / (data.length - 1)) * chartWidth
        const upperY = height - padding - ((upperBound - minValue) / range) * chartHeight

        if (i === 0) {
          ctx.lineTo(x, upperY)
        } else if (i === 1) {
          ctx.lineTo(x, upperY)
        } else {
          // 使用贝塞尔曲线创建平滑效果
          const prevX = padding + ((i - 1) / (data.length - 1)) * chartWidth
          const cpX = (prevX + x) / 2
          ctx.bezierCurveTo(cpX, upperY, cpX, upperY, x, upperY)
        }
      }

      // 下边界线 - 反向绘制
      for (let i = data.length - 1; i >= 0; i--) {
        const x = padding + (i / (data.length - 1)) * chartWidth
        const lowerY = height - padding - ((lowerBound - minValue) / range) * chartHeight

        if (i === data.length - 1) {
          ctx.lineTo(x, lowerY)
        } else if (i === data.length - 2) {
          ctx.lineTo(x, lowerY)
        } else {
          // 使用贝塞尔曲线创建平滑效果
          const nextX = padding + ((i + 1) / (data.length - 1)) * chartWidth
          const cpX = (nextX + x) / 2
          ctx.bezierCurveTo(cpX, lowerY, cpX, lowerY, x, lowerY)
        }
      }

      ctx.closePath()

      // 使用更精细的渐变填充
      const gradient = ctx.createLinearGradient(0, padding, 0, height - padding)
      gradient.addColorStop(0, 'rgba(33, 150, 243, 0.15)')
      gradient.addColorStop(0.5, 'rgba(33, 150, 243, 0.08)')
      gradient.addColorStop(1, 'rgba(33, 150, 243, 0.03)')
      ctx.setFillStyle(gradient)
      ctx.fill()

      // 绘制误差带边界线
      ctx.setStrokeStyle('rgba(33, 150, 243, 0.3)')
      ctx.setLineWidth(1)
      ctx.setLineDash([3, 3]) // 虚线效果

      // 上边界线
      ctx.beginPath()
      for (let i = 0; i < data.length; i++) {
        const x = padding + (i / (data.length - 1)) * chartWidth
        const upperY = height - padding - ((upperBound - minValue) / range) * chartHeight

        if (i === 0) {
          ctx.moveTo(x, upperY)
        } else {
          ctx.lineTo(x, upperY)
        }
      }
      ctx.stroke()

      // 下边界线
      ctx.beginPath()
      for (let i = 0; i < data.length; i++) {
        const x = padding + (i / (data.length - 1)) * chartWidth
        const lowerY = height - padding - ((lowerBound - minValue) / range) * chartHeight

        if (i === 0) {
          ctx.moveTo(x, lowerY)
        } else {
          ctx.lineTo(x, lowerY)
        }
      }
      ctx.stroke()

      // 重置线条样式
      ctx.setLineDash([])
    }
    
    // 绘制折线图 - 使用平滑滤波
    const drawLineChart = (ctx, data, width, height, padding, minValue, range) => {
      if (data.length < 2) return

      const chartWidth = width - padding * 2
      const chartHeight = height - padding * 2

      // 准备原始数据点
      const originalPoints = data.map((item, index) => {
        const x = padding + (index / (data.length - 1)) * chartWidth
        const value = item.value || item.doseRate || 0
        const y = height - padding - ((value - minValue) / range) * chartHeight
        return { x, y }
      })

      // 使用平滑滤波生成平滑点
      const smoothPoints = createSmoothPoints(originalPoints)

      // 绘制渐变填充
      ctx.beginPath()
      ctx.moveTo(padding, height - padding)

      // 绘制平滑路径
      smoothPoints.forEach((point, index) => {
        if (index === 0) {
          ctx.lineTo(point.x, point.y)
        } else {
          ctx.lineTo(point.x, point.y)
        }
      })

      ctx.lineTo(width - padding, height - padding)

      // 创建更丰富的渐变填充 - 参考Chart.js的渐变效果
      const gradient = ctx.createLinearGradient(0, padding, 0, height - padding)
      gradient.addColorStop(0, 'rgba(33, 150, 243, 0.4)')
      gradient.addColorStop(0.5, 'rgba(33, 150, 243, 0.2)')
      gradient.addColorStop(1, 'rgba(33, 150, 243, 0.05)')
      ctx.setFillStyle(gradient)
      ctx.fill()

      // 绘制主线条 - 使用平滑滤波的平滑线条
      ctx.beginPath()
      ctx.setStrokeStyle('#2196F3')
      ctx.setLineWidth(2.5)
      ctx.setLineCap('round')
      ctx.setLineJoin('round')

      // 使用二次贝塞尔曲线连接平滑后的数据点
      if (smoothPoints.length > 0) {
        ctx.moveTo(smoothPoints[0].x, smoothPoints[0].y)

        for (let i = 1; i < smoothPoints.length; i++) {
          const current = smoothPoints[i - 1]
          const next = smoothPoints[i]

          if (i === smoothPoints.length - 1) {
            // 最后一个点直接连线
            ctx.lineTo(next.x, next.y)
          } else {
            // 使用二次贝塞尔曲线创建平滑连接
            const nextNext = smoothPoints[i + 1]
            const cpX = next.x
            const cpY = next.y
            const endX = (next.x + nextNext.x) / 2
            const endY = (next.y + nextNext.y) / 2

            ctx.quadraticCurveTo(cpX, cpY, endX, endY)
          }
        }
      }
      ctx.stroke()

      // 绘制数据点 - 显示在平滑后的位置
      smoothPoints.forEach((point, index) => {
        if (index % Math.ceil(smoothPoints.length / 8) === 0 || index === smoothPoints.length - 1) { // 显示关键点
          // 外圈白色边框
          ctx.beginPath()
          ctx.arc(point.x, point.y, 5, 0, 2 * Math.PI)
          ctx.setFillStyle('#ffffff')
          ctx.fill()

          // 内圈蓝色填充
          ctx.beginPath()
          ctx.arc(point.x, point.y, 3, 0, 2 * Math.PI)
          ctx.setFillStyle('#2196F3')
          ctx.fill()

          // 添加微妙的阴影效果
          ctx.beginPath()
          ctx.arc(point.x, point.y + 1, 3, 0, 2 * Math.PI)
          ctx.setFillStyle('rgba(33, 150, 243, 0.2)')
          ctx.fill()
        }
      })
    }
    
    // 绘制柱状图 - 参考Chart.js的柱状图样式
    const drawBarChart = (ctx, data, width, height, padding, minValue, range) => {
      if (data.length === 0) return

      const chartWidth = width - padding * 2
      const chartHeight = height - padding * 2
      const barWidth = Math.min(chartWidth / data.length * 0.8, 40) // 限制最大宽度
      const barSpacing = chartWidth / data.length * 0.2

      data.forEach((item, index) => {
        const x = padding + (index * chartWidth / data.length) + barSpacing / 2
        const value = item.value || item.doseRate || 0
        const barHeight = ((value - minValue) / range) * chartHeight
        const y = height - padding - barHeight

        // 创建更丰富的渐变效果
        const gradient = ctx.createLinearGradient(0, y, 0, height - padding)
        gradient.addColorStop(0, '#2196F3')
        gradient.addColorStop(0.5, '#1976D2')
        gradient.addColorStop(1, '#1565C0')

        // 绘制柱子主体
        ctx.beginPath()
        // 添加圆角效果 - 模拟Chart.js的borderRadius
        const radius = 6
        const rectX = x
        const rectY = y
        const rectWidth = barWidth
        const rectHeight = barHeight

        // 绘制圆角矩形
        ctx.moveTo(rectX + radius, rectY)
        ctx.lineTo(rectX + rectWidth - radius, rectY)
        ctx.quadraticCurveTo(rectX + rectWidth, rectY, rectX + rectWidth, rectY + radius)
        ctx.lineTo(rectX + rectWidth, rectY + rectHeight)
        ctx.lineTo(rectX, rectY + rectHeight)
        ctx.lineTo(rectX, rectY + radius)
        ctx.quadraticCurveTo(rectX, rectY, rectX + radius, rectY)
        ctx.closePath()

        ctx.setFillStyle(gradient)
        ctx.fill()

        // 添加微妙的边框
        ctx.setStrokeStyle('rgba(33, 150, 243, 0.3)')
        ctx.setLineWidth(1)
        ctx.stroke()

        // 添加高光效果
        const highlightGradient = ctx.createLinearGradient(0, y, 0, y + barHeight * 0.3)
        highlightGradient.addColorStop(0, 'rgba(255, 255, 255, 0.3)')
        highlightGradient.addColorStop(1, 'rgba(255, 255, 255, 0)')

        ctx.beginPath()
        ctx.moveTo(rectX + radius, rectY)
        ctx.lineTo(rectX + rectWidth - radius, rectY)
        ctx.quadraticCurveTo(rectX + rectWidth, rectY, rectX + rectWidth, rectY + radius)
        ctx.lineTo(rectX + rectWidth, rectY + barHeight * 0.3)
        ctx.lineTo(rectX, rectY + barHeight * 0.3)
        ctx.lineTo(rectX, rectY + radius)
        ctx.quadraticCurveTo(rectX, rectY, rectX + radius, rectY)
        ctx.closePath()

        ctx.setFillStyle(highlightGradient)
        ctx.fill()
      })
    }
    
    // 绘制坐标轴和数值标签
    const drawAxes = (ctx, width, height, padding) => {
      ctx.setStrokeStyle('#e5e7eb')
      ctx.setLineWidth(1)

      // X轴
      ctx.beginPath()
      ctx.moveTo(padding, height - padding)
      ctx.lineTo(width - padding, height - padding)
      ctx.stroke()

      // Y轴
      ctx.beginPath()
      ctx.moveTo(padding, padding)
      ctx.lineTo(padding, height - padding)
      ctx.stroke()

      // 绘制Y轴数值标签
      const data = getFilteredData()
      if (data.length > 0) {
        const values = data.map(item => item.value || item.doseRate || 0)
        const maxValue = Math.max(...values)
        const minValue = Math.min(...values)
        const range = maxValue - minValue || 1

        ctx.setFillStyle('#666')
        ctx.setFontSize(10)
        ctx.setTextAlign('right')

        // 绘制5个Y轴标签
        for (let i = 0; i <= 4; i++) {
          const value = minValue + (range * i / 4)
          const y = height - padding - (i * (height - 2 * padding) / 4)
          ctx.fillText(value.toFixed(2), padding - 5, y + 3)
        }

        // 绘制X轴时间标签
        ctx.setTextAlign('center')
        const timeLabels = Math.min(5, data.length)
        for (let i = 0; i < timeLabels; i++) {
          const dataIndex = Math.floor(i * (data.length - 1) / (timeLabels - 1))
          const item = data[dataIndex]
          const x = padding + (i * (width - 2 * padding) / (timeLabels - 1))

          if (item && item.timestamp) {
            const time = new Date(item.timestamp)
            const timeStr = time.getHours().toString().padStart(2, '0') + ':' +
                           time.getMinutes().toString().padStart(2, '0')
            ctx.fillText(timeStr, x, height - padding + 15)
          }
        }
      }
    }
    
    onMounted(() => {
      setTimeout(() => {
        initChart()
      }, 100)
    })
    
    watch(() => props.data, () => {
      drawChart()
    }, { deep: true })
    
    return {
      selectedPeriod,
      timePeriods,
      statistics,
      selectPeriod
    }
  }
}
</script>

<style scoped>
.advanced-chart-container {
  background: linear-gradient(135deg,
    rgba(255, 255, 255, 0.98) 0%,
    rgba(248, 250, 252, 0.95) 50%,
    rgba(240, 245, 251, 0.92) 100%);
  border-radius: 24px;
  padding: 20px;
  margin: 0;
  width: 100%;
  box-sizing: border-box;
  box-shadow:
    0 12px 40px rgba(0, 0, 0, 0.1),
    0 6px 20px rgba(0, 0, 0, 0.06),
    0 2px 8px rgba(0, 0, 0, 0.04);
  border: 1px solid rgba(255, 255, 255, 0.4);
  backdrop-filter: blur(25px);
  -webkit-backdrop-filter: blur(25px);
  position: relative;
  overflow: hidden;
  transform-origin: center;
  transition: all 0.4s cubic-bezier(0.4, 0, 0.2, 1);
}

.advanced-chart-container::before {
  content: '';
  position: absolute;
  top: 0;
  right: 0;
  width: 150px;
  height: 150px;
  background: radial-gradient(circle at center,
    rgba(59, 130, 246, 0.12) 0%,
    rgba(34, 197, 94, 0.08) 40%,
    transparent 70%);
  border-radius: 50%;
  transform: translate(60%, -60%);
  animation: backgroundPulse 6s ease-in-out infinite;
}

@keyframes backgroundPulse {
  0%, 100% {
    transform: translate(60%, -60%) scale(1);
    opacity: 0.8;
  }
  50% {
    transform: translate(60%, -60%) scale(1.1);
    opacity: 1;
  }
}

.advanced-chart-container:hover {
  transform: translateY(-4px) scale(1.01);
  box-shadow:
    0 16px 48px rgba(0, 0, 0, 0.12),
    0 8px 24px rgba(0, 0, 0, 0.08),
    0 4px 12px rgba(0, 0, 0, 0.06);
}

.advanced-chart-container:hover::before {
  animation-duration: 3s;
}

/* 简化的图表头部样式 - 图标和标题同行 */
.chart-header-simple {
  display: flex;
  align-items: center;
  gap: 14px;
  margin-bottom: 18px;
  position: relative;
  z-index: 1;
  padding: 8px 0;
}

.chart-header-modern {
  display: flex;
  flex-direction: column;
  gap: 16px;
  margin-bottom: 20px;
  position: relative;
  z-index: 1;
}

.header-main {
  display: flex;
  align-items: center;
  gap: 16px;
}

.chart-icon-wrapper {
  width: 40px;
  height: 40px;
  background: linear-gradient(135deg,
    #3b82f6 0%,
    #1d4ed8 50%,
    #1e40af 100%);
  border-radius: 12px;
  display: flex;
  align-items: center;
  justify-content: center;
  box-shadow:
    0 8px 24px rgba(59, 130, 246, 0.3),
    0 4px 12px rgba(59, 130, 246, 0.2);
  position: relative;
  transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
}

.chart-icon-wrapper::before {
  content: '';
  position: absolute;
  top: -2px;
  left: -2px;
  right: -2px;
  bottom: -2px;
  background: linear-gradient(135deg,
    rgba(59, 130, 246, 0.4) 0%,
    rgba(29, 78, 216, 0.4) 100%);
  border-radius: 14px;
  opacity: 0;
  transition: opacity 0.3s ease;
  z-index: -1;
}

.chart-icon-wrapper:hover {
  transform: translateY(-2px) scale(1.05);
  box-shadow:
    0 12px 32px rgba(59, 130, 246, 0.4),
    0 6px 16px rgba(59, 130, 246, 0.3);
}

.chart-icon-wrapper:hover::before {
  opacity: 1;
}

.chart-icon-wrapper svg {
  width: 22px;
  height: 22px;
  color: #ffffff;
  transition: transform 0.3s ease;
}

.chart-icon-wrapper:hover svg {
  transform: scale(1.1);
}

.chart-title-modern {
  font-size: 19px;
  font-weight: 700;
  color: #0f172a;
  display: block;
  line-height: 1.2;
  background: linear-gradient(135deg, #0f172a 0%, #374151 100%);
  -webkit-background-clip: text;
  -webkit-text-fill-color: transparent;
  background-clip: text;
  transition: all 0.3s ease;
}

.chart-subtitle-modern {
  font-size: 14px;
  color: #64748b;
  display: block;
  margin-top: 4px;
  font-weight: 500;
  transition: color 0.3s ease;
}

.advanced-chart-container:hover .chart-title-modern {
  background: linear-gradient(135deg, #3b82f6 0%, #1d4ed8 100%);
  -webkit-background-clip: text;
  -webkit-text-fill-color: transparent;
  background-clip: text;
}

.advanced-chart-container:hover .chart-subtitle-modern {
  color: #3b82f6;
}

.header-controls {
  display: flex;
  align-items: center;
  gap: 16px;
  flex-wrap: wrap;
}

.time-selector {
  display: flex;
  gap: 8px;
  background: rgba(248, 250, 252, 0.8);
  border-radius: 12px;
  padding: 4px;
}

.time-option {
  padding: 8px 16px;
  border-radius: 8px;
  font-size: 14px;
  color: #64748b;
  cursor: pointer;
  transition: all 0.3s ease;
}

.time-option.active {
  background: #3b82f6;
  color: #ffffff;
  font-weight: 500;
}

.chart-type-toggle {
  display: flex;
  gap: 8px;
}

.toggle-option {
  width: 40px;
  height: 40px;
  border-radius: 10px;
  background: rgba(248, 250, 252, 0.8);
  display: flex;
  align-items: center;
  justify-content: center;
  cursor: pointer;
  transition: all 0.3s ease;
}

.toggle-option.active {
  background: #3b82f6;
}

.toggle-option svg {
  width: 20px;
  height: 20px;
  color: #64748b;
}

.toggle-option.active svg {
  color: #ffffff;
}

/* 现代化时间选择器 */
.time-selector-modern {
  display: flex;
  gap: 6px;
  background: rgba(248, 250, 252, 0.8);
  border-radius: 16px;
  padding: 6px;
  border: 1px solid rgba(255, 255, 255, 0.5);
}

.time-option-modern {
  padding: 8px 14px;
  border-radius: 12px;
  font-size: 12px;
  color: #64748b;
  cursor: pointer;
  transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
  font-weight: 600;
  min-width: 32px;
  text-align: center;
}

.time-option-modern:hover {
  background: rgba(59, 130, 246, 0.1);
  color: #3b82f6;
}

.time-option-modern.active {
  background: linear-gradient(135deg, #3b82f6 0%, #1d4ed8 100%);
  color: #ffffff;
  box-shadow: 0 4px 12px rgba(59, 130, 246, 0.3);
  transform: translateY(-1px);
}

/* 现代化图表类型切换 */
.chart-type-toggle-modern {
  display: flex;
  gap: 6px;
  background: rgba(248, 250, 252, 0.8);
  border-radius: 16px;
  padding: 6px;
  border: 1px solid rgba(255, 255, 255, 0.5);
}

.toggle-option-modern {
  width: 36px;
  height: 36px;
  border-radius: 12px;
  display: flex;
  align-items: center;
  justify-content: center;
  cursor: pointer;
  transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
}

.toggle-option-modern:hover {
  background: rgba(59, 130, 246, 0.1);
}

.toggle-option-modern svg {
  width: 18px;
  height: 18px;
  color: #64748b;
  transition: all 0.3s ease;
}

.toggle-option-modern.active {
  background: linear-gradient(135deg, #3b82f6 0%, #1d4ed8 100%);
  box-shadow: 0 4px 12px rgba(59, 130, 246, 0.3);
  transform: translateY(-1px);
}

.toggle-option-modern.active svg {
  color: #ffffff;
}

.chart-canvas-container {
  position: relative;
  margin-bottom: 48rpx;
  padding: 16rpx;
  background: linear-gradient(135deg,
    rgba(255, 255, 255, 0.9) 0%,
    rgba(248, 250, 252, 0.8) 100%);
  border-radius: 40rpx;
  border: 1px solid rgba(226, 232, 240, 0.3);
  transition: all 0.3s ease;
  width: 100%;
  box-sizing: border-box;
}

.chart-canvas-container:hover {
  background: linear-gradient(135deg,
    rgba(255, 255, 255, 0.95) 0%,
    rgba(248, 250, 252, 0.9) 100%);
  border-color: rgba(59, 130, 246, 0.2);
  box-shadow: 0 4px 16px rgba(0, 0, 0, 0.06);
}

.chart-canvas {
  width: 100%;
  height: auto;
  border-radius: 16rpx;
  background: linear-gradient(135deg,
    rgba(255, 255, 255, 0.95) 0%,
    rgba(250, 251, 252, 0.9) 100%);
  border: 1px solid rgba(226, 232, 240, 0.4);
  box-shadow:
    inset 0 4rpx 16rpx rgba(0, 0, 0, 0.03),
    0 4rpx 16rpx rgba(0, 0, 0, 0.02);
  transition: all 0.3s ease;
  display: block;
  max-width: 100%;
}

.chart-canvas:hover {
  box-shadow:
    inset 0 2px 12px rgba(0, 0, 0, 0.04),
    0 4px 12px rgba(0, 0, 0, 0.04);
}

.error-band-indicator {
  display: flex;
  justify-content: center;
  margin-top: 16px;
  padding: 8px 12px;
  background: rgba(59, 130, 246, 0.05);
  border-radius: 12px;
  border: 1px solid rgba(59, 130, 246, 0.1);
}

.indicator-item {
  display: flex;
  align-items: center;
  gap: 10px;
  transition: all 0.3s ease;
}

.indicator-item:hover {
  transform: scale(1.05);
}

.indicator-color {
  width: 20px;
  height: 6px;
  border-radius: 3px;
  box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
  transition: all 0.3s ease;
}

.indicator-item:hover .indicator-color {
  box-shadow: 0 4px 8px rgba(0, 0, 0, 0.15);
}

.indicator-text {
  font-size: 13px;
  color: #475569;
  font-weight: 500;
  transition: color 0.3s ease;
}

.indicator-item:hover .indicator-text {
  color: #3b82f6;
}

.chart-statistics {
  display: grid;
  grid-template-columns: repeat(2, 1fr);
  gap: 24rpx;
  margin-top: 40rpx;
}

.stat-item {
  text-align: center;
  padding: 24rpx;
  background: rgba(248, 250, 252, 0.6);
  border-radius: 24rpx;
  border: 1px solid rgba(226, 232, 240, 0.3);
  transition: all 0.3s ease;
}

.stat-item:hover {
  background: rgba(248, 250, 252, 0.8);
  transform: translateY(-2rpx);
  box-shadow: 0 8rpx 24rpx rgba(0, 0, 0, 0.06);
}

.stat-label {
  display: block;
  font-size: 24rpx;
  color: #64748b;
  margin-bottom: 8rpx;
  font-weight: 500;
}

.stat-value {
  display: block;
  font-size: 32rpx;
  font-weight: 700;
  color: #1e293b;
}

/* 响应式调整 */
@media (max-width: 750rpx) {
  .chart-statistics {
    grid-template-columns: repeat(2, 1fr);
    gap: 16rpx;
  }

  .stat-item {
    padding: 20rpx;
  }

  .stat-label {
    font-size: 22rpx;
  }

  .stat-value {
    font-size: 28rpx;
  }
}
</style>
