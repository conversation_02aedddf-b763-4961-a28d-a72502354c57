<template>
  <view class="container">
    <!-- Toast容器 -->
    <ToastContainer />

    <!-- 现代化顶部头部 -->
    <view class="modern-header">
      <view class="header-background">
        <view class="header-gradient"></view>
      </view>
      <view class="header-content">
        <view class="header-left">
          <view class="app-logo">
            <svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2.5" stroke-linecap="round" stroke-linejoin="round">
              <path d="M12 2v20M17 5H9.5a3.5 3.5 0 0 0 0 7h5a3.5 3.5 0 0 1 0 7H6"></path>
            </svg>
          </view>
          <view class="header-info">
            <text class="app-title">辐射监测中心</text>
            <text class="app-subtitle">{{ currentTime }} • 实时监控</text>
          </view>
        </view>
        <view class="header-right">
          <view class="status-indicators">
            <view class="connection-status" :class="{ connected: deviceState.isConnected }">
              <view class="status-dot"></view>
            </view>
            <view class="notification-button" @click="navigateToNotification">
              <svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round">
                <path d="M18 8A6 6 0 0 0 6 8c0 7-3 9-3 9h18s-3-2-3-9"></path>
                <path d="M13.73 21a2 2 0 0 1-3.46 0"></path>
              </svg>
              <view class="notification-badge" v-if="hasUnreadNotifications">{{ unreadCount }}</view>
            </view>
          </view>
        </view>
      </view>
    </view>

    <!-- 重新设计的实时辐射监测卡片 -->
    <view class="radiation-monitoring-card-redesigned gpu-accelerated interactive-trigger smart-shadow magnetic-element">
      <!-- 卡片头部 -->
      <view class="card-header-redesigned">
        <view class="header-left-section">
          <view class="radiation-icon-large" :class="radiationLevelClass">
            <svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2.5" stroke-linecap="round" stroke-linejoin="round">
              <circle cx="12" cy="12" r="10"></circle>
              <circle cx="12" cy="12" r="6"></circle>
              <circle cx="12" cy="12" r="2"></circle>
            </svg>
          </view>
          <view class="header-text-section">
            <text class="main-title typewriter-text">实时监测</text>
            <text class="sub-title">{{ radiationStatusText }} • {{ currentTime }}</text>
          </view>
        </view>

        <view class="status-indicators-redesigned">
          <view class="status-badge breathing-glow magnetic-element" :class="deviceState.connection.mqtt ? 'online' : 'offline'">
            <text>{{ deviceState.connection.mqtt ? '在线' : '离线' }}</text>
          </view>
          <view class="status-badge breathing-glow magnetic-element" :class="isCollecting ? 'monitoring' : 'paused'">
            <text>{{ isCollecting ? '监测中' : '已暂停' }}</text>
          </view>
        </view>
      </view>

      <!-- 主要数据显示区域 -->
      <view class="main-data-section">
        <!-- 中央大数值显示 -->
        <view class="central-value-display">
          <text class="dose-rate-number breathing-glow" :class="radiationLevelClass">{{ formatDoseRate(radiationState.currentData.doseRate) }}</text>
          <text class="dose-rate-unit">μSv/h</text>
        </view>

        <!-- 关键信息网格 -->
        <view class="info-grid-redesigned">
          <view class="info-card">
            <view class="info-icon shield">
              <svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round">
                <path d="M12 22s8-4 8-10V5l-8-3-8 3v7c0 6 8 10 8 10z"></path>
                <path d="M9 12l2 2 4-4"></path>
              </svg>
            </view>
            <view class="info-content">
              <text class="info-label">安全阈值</text>
              <text class="info-value">0.20 μSv/h</text>
            </view>
          </view>

          <view class="info-card">
            <view class="info-icon dollar">
              <svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round">
                <path d="M12 2v20"></path>
                <path d="M17 5H9.5a3.5 3.5 0 0 0 0 7h5a3.5 3.5 0 0 1 0 7H6"></path>
              </svg>
            </view>
            <view class="info-content">
              <text class="info-label">今日累积</text>
              <text class="info-value">{{ formatNumber(radiationState.currentData.doseSum, 3) }} μSv</text>
            </view>
          </view>

          <view class="info-card">
            <view class="info-icon clock">
              <svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round">
                <circle cx="12" cy="12" r="10"></circle>
                <polyline points="12,6 12,12 16,14"></polyline>
              </svg>
            </view>
            <view class="info-content">
              <text class="info-label">监测时长</text>
              <text class="info-value">{{ getMonitoringDuration() }}</text>
            </view>
          </view>
        </view>
      </view>

      <!-- 底部数据网格 -->
      <view class="bottom-data-grid">
        <view class="data-item">
          <view class="data-icon activity">
            <svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round">
              <path d="M22 12h-4l-3 9L9 3l-3 9H2"></path>
            </svg>
          </view>
          <view class="data-details">
            <text class="data-value">{{ formatNumber(radiationState.currentData.cps, 0) }}</text>
            <text class="data-label">计数率 CPS</text>
          </view>
        </view>

        <view class="data-item">
          <view class="data-icon dollar">
            <svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round">
              <path d="M12 2v20M17 5H9.5a3.5 3.5 0 0 0 0 7h5a3.5 3.5 0 0 1 0 7H6"></path>
            </svg>
          </view>
          <view class="data-details">
            <text class="data-value">{{ formatNumber(radiationState.currentData.doseSum) }}</text>
            <text class="data-label">累积剂量 μSv</text>
          </view>
        </view>

        <view class="data-item">
          <view class="data-icon thermometer">
            <svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round">
              <path d="M14 14.76V3.5a2.5 2.5 0 0 0-5 0v11.26a4.5 4.5 0 1 0 5 0z"></path>
            </svg>
          </view>
          <view class="data-details">
            <text class="data-value">{{ formatTemperature(radiationState.currentData.temperature) }}</text>
            <text class="data-label">环境温度</text>
          </view>
        </view>

        <view class="data-item">
          <view class="data-icon battery">
            <svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round">
              <rect x="1" y="3" width="15" height="13"></rect>
              <polygon points="16,8 20,8 20,16 16,16"></polygon>
            </svg>
          </view>
          <view class="data-details">
            <text class="data-value">{{ formatPercentage(deviceState.battery.level) }}</text>
            <text class="data-label">电池电量</text>
          </view>
        </view>
      </view>

      <!-- 底部操作按钮 -->
      <view class="card-actions">
        <view class="action-btn primary liquid-button magnetic-element" @click="toggleCollection">
          <text>{{ isCollecting ? '暂停监测' : '开始监测' }}</text>
        </view>
        <view class="action-btn secondary liquid-button magnetic-element data-stream" @click="refreshData">
          <text>刷新数据</text>
        </view>
      </view>
    </view>

    <!-- 设备状态摘要 - 参考home.html的device-summary设计 -->
    <view class="device-summary">
      <view class="summary-item">
        <view class="summary-value">{{ formatNumber(radiationState.currentData.cps, 0) }}</view>
        <view class="summary-label">计数率 CPS</view>
      </view>
      <view class="summary-item">
        <view class="summary-value">{{ formatNumber(radiationState.currentData.doseSum) }}</view>
        <view class="summary-label">累积剂量 μSv</view>
      </view>
      <view class="summary-item">
        <view class="summary-value">{{ formatNumber(radiationState.currentData.temperature, 1) }}</view>
        <view class="summary-label">环境温度 °C</view>
      </view>
      <view class="summary-item">
        <view class="summary-circle">
          <view :class="['status-dot', deviceState.connection.mqtt ? '' : 'error']"></view>
        </view>
        <view class="summary-label">设备状态</view>
      </view>
    </view>

    <!-- 主要内容区域 -->
    <view class="main-content">
      <!-- 优化的波形显示区域 -->
      <view class="charts-container-optimized gpu-accelerated">
        <AdvancedChart
          :title="'剂量率监测'"
          :subtitle="'实时数据 • 平滑曲线'"
          :data="chartData"
          :canvas-id="'doseRateChart'"
          :chart-width="contentWidth"
          :chart-height="chartHeight"
          :show-error-band="false"
        />

        <AdvancedChart
          :title="'计数率监测'"
          :subtitle="'CPS数据 • 统计分析'"
          :data="cpsChartData"
          :canvas-id="'cpsChart'"
          :chart-width="contentWidth"
          :chart-height="chartHeight"
          :show-error-band="false"
        />
      </view>

      <!-- 剂量率监测卡片 - 新增时间筛选和网格显示 -->
      <view class="monitoring-section">
        <view class="section-header-modern">
          <view class="header-left">
            <view class="section-icon-wrapper">
              <svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round">
                <path d="M12 2L2 7l10 5 10-5-10-5z"></path>
                <path d="M2 17l10 5 10-5"></path>
                <path d="M2 12l10 5 10-5"></path>
              </svg>
            </view>
            <view class="header-text">
              <text class="section-title-modern">剂量率监测</text>
              <text class="section-subtitle-modern">超标时间段分析</text>
            </view>
          </view>

        </view>

        <!-- 日历式时间筛选器 -->
        <view class="time-filter-container floating-element">
          <view class="calendar-filter-card">
            <view class="date-inputs-section">
              <view class="date-input-group">
                <text class="date-label">开始时间</text>
                <picker
                  mode="multiSelector"
                  :range="dateTimeRange"
                  :value="startDateTimeIndex"
                  @change="onStartDateTimeChange"
                  class="datetime-picker"
                >
                  <view class="date-input-wrapper">
                    <text class="date-display">{{ formatDateDisplay(selectedStartDate) }}</text>
                    <svg class="calendar-icon" xmlns="http://www.w3.org/2000/svg" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round">
                      <rect x="3" y="4" width="18" height="18" rx="2" ry="2"></rect>
                      <line x1="16" y1="2" x2="16" y2="6"></line>
                      <line x1="8" y1="2" x2="8" y2="6"></line>
                      <line x1="3" y1="10" x2="21" y2="10"></line>
                    </svg>
                  </view>
                </picker>
              </view>
              <view class="date-input-group">
                <text class="date-label">结束时间</text>
                <picker
                  mode="multiSelector"
                  :range="dateTimeRange"
                  :value="endDateTimeIndex"
                  @change="onEndDateTimeChange"
                  class="datetime-picker"
                >
                  <view class="date-input-wrapper">
                    <text class="date-display">{{ formatDateDisplay(selectedEndDate) }}</text>
                    <svg class="calendar-icon" xmlns="http://www.w3.org/2000/svg" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round">
                      <rect x="3" y="4" width="18" height="18" rx="2" ry="2"></rect>
                      <line x1="16" y1="2" x2="16" y2="6"></line>
                      <line x1="8" y1="2" x2="8" y2="6"></line>
                      <line x1="3" y1="10" x2="21" y2="10"></line>
                    </svg>
                  </view>
                </picker>
              </view>
            </view>
            <view class="preset-buttons">
              <button class="preset-btn" :class="{ active: selectedPreset === '1h' }" @click="setPresetTimeWithAnimation('1h')">最近1小时</button>
              <button class="preset-btn" :class="{ active: selectedPreset === '6h' }" @click="setPresetTimeWithAnimation('6h')">最近6小时</button>
              <button class="preset-btn" :class="{ active: selectedPreset === '24h' }" @click="setPresetTimeWithAnimation('24h')">最近24小时</button>
            </view>
          </view>
        </view>

        <view class="section-content"
        </view>

        <!-- 矩阵网格显示超标时刻 -->
        <view class="radiation-matrix">
          <view
            v-for="(cell, index) in radiationMatrixCells"
            :key="index"
            class="matrix-cell"
            :class="cell.status"
            @click="viewCellDetails(cell)"
          >
            <text v-if="cell.hasData" class="cell-value">{{ cell.displayValue }}</text>
          </view>
        </view>

        <!-- 矩阵图例 -->
        <view class="matrix-legend">
          <view class="legend-item">
            <view class="legend-color safe"></view>
            <text class="legend-text">安全</text>
          </view>
          <view class="legend-item">
            <view class="legend-color warning"></view>
            <text class="legend-text">警告</text>
          </view>
          <view class="legend-item">
            <view class="legend-color danger"></view>
            <text class="legend-text">超标</text>
          </view>
          <view class="legend-item">
            <view class="legend-color no-data"></view>
            <text class="legend-text">无数据</text>
          </view>
        </view>
      </view>





      <!-- 快捷操作 -->
      <view class="section-title">
        🎛️ 快捷操作
        <text class="section-subtitle">数据管理</text>
      </view>
      <view class="mode-cards-container">
        <view class="mode-card flip-card floating-particle" @click="goToCharts">
          <view class="mode-icon">
            <svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round">
              <path d="M22 11.08V12a10 10 0 1 1-5.93-9.14"></path>
              <polyline points="22 4 12 14.01 9 11.01"></polyline>
            </svg>
          </view>
          <view class="mode-name">历史图表</view>
        </view>
        <view class="mode-card flip-card floating-particle" @click="exportData">
          <view class="mode-icon">
            <svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round">
              <rect x="3" y="3" width="18" height="18" rx="2" ry="2"></rect>
              <line x1="9" y1="9" x2="15" y2="15"></line>
              <line x1="15" y1="9" x2="9" y2="15"></line>
            </svg>
          </view>
          <view class="mode-name">导出数据</view>
        </view>
        <view class="mode-card" @click="goToMap">
          <view class="mode-icon">
            <svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round">
              <circle cx="12" cy="12" r="10"></circle>
              <path d="M12 16v-4"></path>
              <path d="M12 8h.01"></path>
            </svg>
          </view>
          <view class="mode-name">位置信息</view>
        </view>
        <view class="mode-card" @click="navigateToSettings">
          <view class="mode-icon">
            <svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round">
              <path d="M12 20V10"></path>
              <path d="M18 20V4"></path>
              <path d="M6 20v-6"></path>
            </svg>
          </view>
          <view class="mode-name">系统设置</view>
        </view>
      </view>
    </view>

    <!-- 底部导航栏 -->
    <BottomNavigation currentPage="dashboard" />
  </view>
</template>

<script>
import { ref, computed, onMounted, onUnmounted } from 'vue'
import { radiationState, deviceState, locationState } from '../../utils/dataStore.js'
import mqttService from '../../utils/mqttService.js'
import dataStore from '../../utils/dataStore.js'
import ToastContainer from '../../components/ToastContainer.vue'
import AdvancedChart from '../../components/AdvancedChart.vue'
import toastManager from '../../utils/toastManager.js'
import BottomNavigation from '../../components/BottomNavigation.vue'

export default {
  name: 'Dashboard',
  components: {
    ToastContainer,
    AdvancedChart,
    BottomNavigation
  },
  setup() {
    const currentTime = ref('')
    const isCollecting = ref(true)
    const showConnectionStatus = ref(false)
    const timeInterval = ref(null)
    const chartContext = ref(null)
    const lastUpdateTime = ref('')
    const contentWidth = ref(680)  // 使用rpx单位，适合小程序
    const chartHeight = ref(480)   // 使用rpx单位，适合小程序

    // 通知相关
    const hasUnreadNotifications = ref(true)
    const unreadCount = ref(3)

    // 导航栏状态
    const activeTabIndex = ref(0)

    const setActiveTab = (index) => {
      activeTabIndex.value = index
    }

    // 统一的导航函数（支持tabBar与普通页面）
    const navigateTo = (page) => {
      const routes = {
        'dashboard': '/pages/dashboard/dashboard',
        'charts': '/pages/charts/charts',
        'health': '/pages/health/health',
        'map': '/pages/map/map',
        'settings': '/pages/settings/settings',
        'notification': '/pages/notification/notification'
      }
      const tabBarPages = ['dashboard', 'charts', 'health', 'map', 'settings']

      if (routes[page]) {
        uni.navigateTo({
          url: routes[page],
          fail: (err) => {
            console.error('导航失败:', err)
            uni.showToast({
              title: '页面跳转失败',
              icon: 'none'
            })
          }
        })
      }
    }

    // 图表数据
    const chartData = computed(() => {
      return radiationState.history.map(item => ({
        value: item.doseRate,
        timestamp: item.timestamp,
        time: item.timestamp
      }))
    })

    const cpsChartData = computed(() => {
      return radiationState.history.map(item => ({
        value: item.cps,
        timestamp: item.timestamp,
        time: item.timestamp
      }))
    })

    // 导航项
    const navItems = ref([
      { text: '实时', emoji: '📊' },
      { text: '图表', emoji: '📈' },
      { text: '地图', emoji: '🗺️' },
      { text: '设置', emoji: '⚙️' }
    ])

    // 监测相关数据
    const selectedMonitoringPeriod = ref('24h')
    const monitoringPeriods = ref([
      { label: '1小时', value: '1h' },
      { label: '6小时', value: '6h' },
      { label: '24小时', value: '24h' },
      { label: '7天', value: '7d' }
    ])

    // 日历式时间选择
    const selectedStartDate = ref(new Date())
    const selectedEndDate = ref(new Date())
    const selectedPreset = ref('24h')
    const customStartTime = ref('')
    const customEndTime = ref('')

    // 生成日期时间选择器的范围数据
    const generateDateTimeRange = () => {
      const currentYear = new Date().getFullYear()
      const years = []
      const months = []
      const days = []
      const hours = []
      const minutes = []

      // 年份：当前年份前后各2年
      for (let i = currentYear - 2; i <= currentYear + 2; i++) {
        years.push(i.toString())
      }

      // 月份：1-12
      for (let i = 1; i <= 12; i++) {
        months.push(i.toString().padStart(2, '0'))
      }

      // 日期：1-31
      for (let i = 1; i <= 31; i++) {
        days.push(i.toString().padStart(2, '0'))
      }

      // 小时：0-23
      for (let i = 0; i <= 23; i++) {
        hours.push(i.toString().padStart(2, '0'))
      }

      // 分钟：0-59，每5分钟一个选项
      for (let i = 0; i <= 59; i += 5) {
        minutes.push(i.toString().padStart(2, '0'))
      }

      return [years, months, days, hours, minutes]
    }

    const dateTimeRange = ref(generateDateTimeRange())
    const startDateTimeIndex = ref([2, 0, 0, 0, 0]) // 默认当前年份
    const endDateTimeIndex = ref([2, 0, 0, 0, 0])

    // 初始化自定义时间为最近24小时
    const initCustomTime = () => {
      const now = new Date()
      const yesterday = new Date(now.getTime() - 24 * 60 * 60 * 1000)

      selectedStartDate.value = yesterday
      selectedEndDate.value = now
      selectedPreset.value = '24h'

      // 更新picker索引
      updatePickerIndex(yesterday, true)
      updatePickerIndex(now, false)

      customEndTime.value = now.toISOString().slice(0, 16)
      customStartTime.value = yesterday.toISOString().slice(0, 16)
    }

    // 模拟监测时间段数据
    const monitoringTimeSlots = computed(() => {
      const slots = []
      const now = new Date()
      const periodHours = selectedMonitoringPeriod.value === '1h' ? 1 :
                         selectedMonitoringPeriod.value === '6h' ? 6 :
                         selectedMonitoringPeriod.value === '24h' ? 24 : 168

      const slotCount = selectedMonitoringPeriod.value === '7d' ? 28 : 12
      const hoursPerSlot = periodHours / slotCount

      for (let i = 0; i < slotCount; i++) {
        const startTime = new Date(now.getTime() - (slotCount - i) * hoursPerSlot * 60 * 60 * 1000)
        const endTime = new Date(now.getTime() - (slotCount - i - 1) * hoursPerSlot * 60 * 60 * 1000)

        // 模拟数据
        const maxDoseRate = 0.08 + Math.random() * 0.15
        const peakValue = maxDoseRate + Math.random() * 0.05
        const status = maxDoseRate > 0.15 ? 'danger' : maxDoseRate > 0.12 ? 'warning' : 'normal'

        slots.push({
          timeRange: `${startTime.getHours().toString().padStart(2, '0')}:${startTime.getMinutes().toString().padStart(2, '0')}-${endTime.getHours().toString().padStart(2, '0')}:${endTime.getMinutes().toString().padStart(2, '0')}`,
          maxDoseRate,
          peakValue,
          status,
          statusText: status === 'danger' ? '超标' : status === 'warning' ? '警告' : '正常',
          duration: `${Math.floor(hoursPerSlot)}h${Math.floor((hoursPerSlot % 1) * 60)}m`,
          startTime,
          endTime
        })
      }

      return slots
    })

    // 辐射监测矩阵数据
    const radiationMatrixCells = computed(() => {
      const cells = []
      const now = new Date()
      const periodHours = selectedMonitoringPeriod.value === '1h' ? 1 :
                         selectedMonitoringPeriod.value === '6h' ? 6 :
                         selectedMonitoringPeriod.value === '24h' ? 24 : 168

      // 根据时间周期确定矩阵大小 - 减少格子数量以适应页面宽度
      let totalCells, columns
      if (periodHours <= 1) {
        totalCells = 30 // 1小时，每2分钟一个格子
        columns = 6
      } else if (periodHours <= 6) {
        totalCells = 36 // 6小时，每10分钟一个格子
        columns = 6
      } else if (periodHours <= 24) {
        totalCells = 48 // 24小时，每30分钟一个格子
        columns = 8
      } else {
        totalCells = 56 // 7天，每3小时一个格子
        columns = 8
      }

      const timePerCell = (periodHours * 60 * 60 * 1000) / totalCells

      for (let i = 0; i < totalCells; i++) {
        const cellStartTime = new Date(now.getTime() - (totalCells - i) * timePerCell)
        const cellEndTime = new Date(cellStartTime.getTime() + timePerCell)

        // 模拟数据 - 基于历史数据或随机生成
        const hasData = Math.random() > 0.1 // 90%的格子有数据
        let status = 'no-data'
        let displayValue = ''
        let tooltip = `${cellStartTime.toLocaleTimeString()} - ${cellEndTime.toLocaleTimeString()}`

        if (hasData) {
          const doseRate = 0.08 + Math.random() * 0.15
          displayValue = formatDoseRate(doseRate)

          if (doseRate > 0.20) {
            status = 'danger'
            tooltip += `\n剂量率: ${displayValue} μSv/h (超标)`
          } else if (doseRate > 0.15) {
            status = 'warning'
            tooltip += `\n剂量率: ${displayValue} μSv/h (警告)`
          } else {
            status = 'safe'
            tooltip += `\n剂量率: ${displayValue} μSv/h (安全)`
          }
        } else {
          tooltip += '\n无数据'
        }

        cells.push({
          index: i,
          startTime: cellStartTime,
          endTime: cellEndTime,
          hasData,
          status,
          displayValue,
          tooltip
        })
      }

      return cells
    })

    // 查看格子详情 - 改为toast通知
    const viewCellDetails = (cell) => {
      if (!cell.hasData) return

      const statusText = cell.status === 'danger' ? '超标' : cell.status === 'warning' ? '警告' : '安全'
      toastManager.info(`${cell.startTime.toLocaleTimeString()} ${statusText}: ${cell.displayValue} μSv/h`, {
        duration: 3000,
        showCountdown: false
      })
    }

    // 计算属性
    const radiationLevel = computed(() => {
      const doseRate = radiationState.currentData.doseRate
      const { maxDoseRate, minDoseRate } = radiationState.settings
      
      if (doseRate > maxDoseRate) return 'danger'
      if (doseRate < minDoseRate) return 'warning'
      return 'safe'
    })

    const radiationLevelClass = computed(() => {
      return `level-${radiationLevel.value}`
    })

    const radiationIcon = computed(() => {
      switch (radiationLevel.value) {
        case 'danger': return '🚨'
        case 'warning': return '⚠️'
        default: return '✅'
      }
    })

    const radiationStatusText = computed(() => {
      switch (radiationLevel.value) {
        case 'danger': return '危险水平'
        case 'warning': return '需要注意'
        default: return '安全水平'
      }
    })

    // 今日统计数据
    const todayAvgDoseRate = computed(() => {
      const today = new Date().toDateString()
      const todayData = radiationState.history.filter(item => 
        new Date(item.timestamp).toDateString() === today
      )
      
      if (todayData.length === 0) return 0
      
      const total = todayData.reduce((sum, item) => sum + item.doseRate, 0)
      return total / todayData.length
    })

    const todayMaxDoseRate = computed(() => {
      const today = new Date().toDateString()
      const todayData = radiationState.history.filter(item => 
        new Date(item.timestamp).toDateString() === today
      )
      
      if (todayData.length === 0) return 0
      
      return Math.max(...todayData.map(item => item.doseRate))
    })

    const todayAlerts = computed(() => {
      const today = new Date().toDateString()
      return radiationState.alerts.filter(alert => 
        new Date(alert.timestamp).toDateString() === today
      ).length
    })

    const lastAlert = computed(() => {
      return radiationState.alerts.length > 0 ? radiationState.alerts[0] : null
    })

    // 方法
    const updateTime = () => {
      const now = new Date()
      currentTime.value = now.toLocaleTimeString('zh-CN', {
        hour: '2-digit',
        minute: '2-digit',
        second: '2-digit'
      })
    }

    const formatDoseRate = (value) => {
      // 统一显示格式：最多两位小数
      return value.toFixed(2)
    }

    // 新增：格式化数值的通用函数
    const formatNumber = (value, decimals = 2) => {
      if (typeof value !== 'number') return '0.00'
      return value.toFixed(decimals)
    }

    // 新增：格式化百分比
    const formatPercentage = (value) => {
      return formatNumber(value, 1) + '%'
    }

    // 新增：格式化温度
    const formatTemperature = (value) => {
      return formatNumber(value, 1) + '°C'
    }

    // 新增：格式化计数率
    const formatCps = (value) => {
      return formatNumber(value, 0) + ' CPS'
    }

    const formatTime = (timestamp) => {
      return new Date(timestamp).toLocaleTimeString('zh-CN', {
        hour: '2-digit',
        minute: '2-digit'
      })
    }

    const toggleCollection = () => {
      isCollecting.value = !isCollecting.value
      toastManager.success(isCollecting.value ? '开始采集数据' : '停止采集数据')
    }

    const exportData = () => {
      try {
        dataStore.exportData()

        uni.showModal({
          title: '导出数据',
          content: '数据已准备就绪，您可以将其保存到文件',
          confirmText: '确定',
          success: (res) => {
            if (res.confirm) {
              toastManager.success('导出成功')
            }
          }
        })
      } catch (error) {
        toastManager.error('导出失败')
      }
    }

    const showSettings = () => {
      uni.navigateTo({
        url: '/pages/settings/settings'
      })
    }

    const navigateToNotification = () => {
      uni.navigateTo({
        url: '/pages/notification/notification'
      })
    }

    const navigateToSettings = () => {
      uni.navigateTo({
        url: '/pages/settings/settings'
      })
    }

    const goToCharts = () => {
      uni.navigateTo({
        url: '/pages/charts/charts'
      })
    }

    const goToMap = () => {
      uni.navigateTo({
        url: '/pages/map/map'
      })
    }



    const refreshData = () => {
      lastUpdateTime.value = new Date().toLocaleTimeString()
      toastManager.info('数据已刷新', {
        duration: 2000,
        showCountdown: false
      })
    }

    // 检查温度异常
    const checkTemperatureAlerts = () => {
      const temperature = radiationState.currentData.temperature
      if (temperature > 40) {
        toastManager.warning(`环境温度过高: ${formatTemperature(temperature)}`, {
          duration: 4000,
          showCountdown: true
        })
      } else if (temperature < -10) {
        toastManager.warning(`环境温度过低: ${formatTemperature(temperature)}`, {
          duration: 4000,
          showCountdown: true
        })
      }
    }

    // 检查计数率异常
    const checkCpsAlerts = () => {
      const cps = radiationState.currentData.cps
      if (cps > 1000) {
        toastManager.warning(`计数率异常: ${formatCps(cps)}`, {
          duration: 4000,
          showCountdown: true
        })
      }
    }

    // 监测相关方法
    const selectMonitoringPeriod = (period) => {
      selectedMonitoringPeriod.value = period
    }

    const viewTimeSlotDetails = (timeSlot) => {
      toastManager.info(`查看 ${timeSlot.timeRange} 详细数据`, {
        duration: 2000,
        showCountdown: false
      })
    }

    // 自定义时间选择方法 - 动态更新矩阵数据
    const updateCustomTimeRange = () => {
      if (customStartTime.value && customEndTime.value) {
        const start = new Date(customStartTime.value)
        const end = new Date(customEndTime.value)
        if (start < end) {
          // 更新矩阵数据，保持总格子数不变，改变步长
          updateMatrixDataWithTimeRange(start, end)
          toastManager.success(`时间范围已更新: ${start.toLocaleString()} - ${end.toLocaleString()}`, {
            duration: 3000,
            showCountdown: false
          })
        } else {
          toastManager.error('开始时间不能晚于结束时间', {
            duration: 3000,
            showCountdown: false
          })
        }
      }
    }

    // 根据时间范围更新矩阵数据
    const updateMatrixDataWithTimeRange = (startTime, endTime) => {
      // 这里可以添加实际的数据更新逻辑
      // 计算时间跨度并调整步长，保持总格子数不变
      const timeDiff = endTime.getTime() - startTime.getTime()
      const totalCells = 48 // 保持总格子数为48
      const stepSize = timeDiff / totalCells

      console.log(`时间范围: ${startTime.toLocaleString()} - ${endTime.toLocaleString()}`)
      console.log(`时间跨度: ${Math.round(timeDiff / (1000 * 60 * 60))} 小时`)
      console.log(`每个格子代表: ${Math.round(stepSize / (1000 * 60))} 分钟`)
    }

    // 动画效果相关方法
    const triggerDataUpdateAnimation = (element) => {
      if (element) {
        element.classList.add('data-updated')
        setTimeout(() => {
          element.classList.remove('data-updated')
        }, 1000)
      }
    }

    const triggerSuccessAnimation = (element) => {
      if (element) {
        element.classList.add('success-bounce')
        setTimeout(() => {
          element.classList.remove('success-bounce')
        }, 1000)
      }
    }

    const triggerErrorAnimation = (element) => {
      if (element) {
        element.classList.add('error-shake')
        setTimeout(() => {
          element.classList.remove('error-shake')
        }, 500)
      }
    }

    // 增强的预设时间设置方法，添加动画效果
    const setPresetTimeWithAnimation = (preset) => {
      setPresetTime(preset)
      // 触发数据更新动画
      nextTick(() => {
        const matrixElement = document.querySelector('.radiation-matrix')
        triggerDataUpdateAnimation(matrixElement)
      })
    }

    // 格式化日期显示
    const formatDateDisplay = (date) => {
      if (!date) return '选择日期'
      const year = date.getFullYear()
      const month = String(date.getMonth() + 1).padStart(2, '0')
      const day = String(date.getDate()).padStart(2, '0')
      const hours = String(date.getHours()).padStart(2, '0')
      const minutes = String(date.getMinutes()).padStart(2, '0')
      return `${year}-${month}-${day}T${hours}:${minutes}`
    }

    // 开始日期时间选择器变化事件
    const onStartDateTimeChange = (e) => {
      const [yearIndex, monthIndex, dayIndex, hourIndex, minuteIndex] = e.detail.value
      const year = parseInt(dateTimeRange.value[0][yearIndex])
      const month = parseInt(dateTimeRange.value[1][monthIndex]) - 1
      const day = parseInt(dateTimeRange.value[2][dayIndex])
      const hour = parseInt(dateTimeRange.value[3][hourIndex])
      const minute = parseInt(dateTimeRange.value[4][minuteIndex])

      const newDate = new Date(year, month, day, hour, minute)
      selectedStartDate.value = newDate
      startDateTimeIndex.value = e.detail.value
      updateMatrixFromDates()
      selectedPreset.value = ''
    }

    // 结束日期时间选择器变化事件
    const onEndDateTimeChange = (e) => {
      const [yearIndex, monthIndex, dayIndex, hourIndex, minuteIndex] = e.detail.value
      const year = parseInt(dateTimeRange.value[0][yearIndex])
      const month = parseInt(dateTimeRange.value[1][monthIndex]) - 1
      const day = parseInt(dateTimeRange.value[2][dayIndex])
      const hour = parseInt(dateTimeRange.value[3][hourIndex])
      const minute = parseInt(dateTimeRange.value[4][minuteIndex])

      const newDate = new Date(year, month, day, hour, minute)
      selectedEndDate.value = newDate
      endDateTimeIndex.value = e.detail.value
      updateMatrixFromDates()
      selectedPreset.value = ''
    }

    // 根据日期更新picker的索引
    const updatePickerIndex = (date, isStart = true) => {
      const year = date.getFullYear()
      const month = date.getMonth() + 1
      const day = date.getDate()
      const hour = date.getHours()
      const minute = Math.floor(date.getMinutes() / 5) * 5 // 取最近的5分钟倍数

      const yearIndex = dateTimeRange.value[0].findIndex(y => parseInt(y) === year)
      const monthIndex = dateTimeRange.value[1].findIndex(m => parseInt(m) === month)
      const dayIndex = dateTimeRange.value[2].findIndex(d => parseInt(d) === day)
      const hourIndex = dateTimeRange.value[3].findIndex(h => parseInt(h) === hour)
      const minuteIndex = dateTimeRange.value[4].findIndex(m => parseInt(m) === minute)

      const index = [
        Math.max(0, yearIndex),
        Math.max(0, monthIndex),
        Math.max(0, dayIndex),
        Math.max(0, hourIndex),
        Math.max(0, minuteIndex)
      ]

      if (isStart) {
        startDateTimeIndex.value = index
      } else {
        endDateTimeIndex.value = index
      }
    }

    // 根据日期更新矩阵
    const updateMatrixFromDates = () => {
      const startTime = selectedStartDate.value
      const endTime = selectedEndDate.value

      customStartTime.value = formatDateDisplay(startTime)
      customEndTime.value = formatDateDisplay(endTime)

      updateCustomTimeRange()
    }

    const setPresetTime = (preset) => {
      const now = new Date()
      let startTime

      switch (preset) {
        case '1h':
          startTime = new Date(now.getTime() - 60 * 60 * 1000)
          break
        case '6h':
          startTime = new Date(now.getTime() - 6 * 60 * 60 * 1000)
          break
        case '24h':
          startTime = new Date(now.getTime() - 24 * 60 * 60 * 1000)
          break
        default:
          startTime = new Date(now.getTime() - 24 * 60 * 60 * 1000)
      }

      selectedStartDate.value = startTime
      selectedEndDate.value = now
      selectedPreset.value = preset

      // 更新picker索引
      updatePickerIndex(startTime, true)
      updatePickerIndex(now, false)

      customStartTime.value = startTime.toISOString().slice(0, 16)
      customEndTime.value = now.toISOString().slice(0, 16)
      updateCustomTimeRange()
    }

    // 计算辐射进度百分比
    const getRadiationProgress = () => {
      const currentDoseRate = radiationState.currentData.doseRate
      const maxSafeLevel = 0.2 // 假设0.2 μSv/h为安全上限
      return Math.min((currentDoseRate / maxSafeLevel) * 100, 100)
    }

    // 新增的交互方法
    const getBatteryLevelClass = () => {
      const level = deviceState.battery.level
      if (level > 60) return 'high'
      if (level > 30) return 'medium'
      return 'low'
    }

    const getStatProgress = (value) => {
      return Math.min((value / 0.5) * 100, 100) // 假设0.5为最大参考值
    }

    const viewDetailedStats = () => {
      uni.navigateTo({
        url: '/pages/charts/charts'
      })
    }

    const viewAlerts = () => {
      uni.showActionSheet({
        itemList: ['查看所有报警', '报警设置', '清除历史'],
        success: (res) => {
          switch (res.tapIndex) {
            case 0:
              toastManager.info('查看报警列表', {
                duration: 2000,
                showCountdown: false
              })
              break
            case 1:
              uni.navigateTo({ url: '/pages/settings/settings' })
              break
            case 2:
              toastManager.success('历史记录已清除', {
                duration: 2000,
                showCountdown: false
              })
              break
          }
        }
      })
    }

    const refreshChart = () => {
      isChartLoading.value = true
      setTimeout(() => {
        drawMiniChart()
        isChartLoading.value = false
      }, 1000)
    }

    const showQuickMenu = () => {
      uni.showActionSheet({
        itemList: ['数据导出', '设备设置', '系统信息', '帮助中心'],
        success: (res) => {
          switch (res.tapIndex) {
            case 0:
              exportData()
              break
            case 1:
              showSettings()
              break
            case 2:
              showConnectionStatus.value = true
              break
            case 3:
              toastManager.info('帮助中心')
              break
          }
        }
      })
    }

    const hideConnectionStatus = () => {
      showConnectionStatus.value = false
    }

    const reconnectDevice = () => {
      uni.showLoading({ title: '重新连接中...' })
      setTimeout(() => {
        uni.hideLoading()
        toastManager.success('连接成功')
        hideConnectionStatus()
      }, 2000)
    }

    const testConnection = () => {
      uni.showLoading({ title: '测试连接中...' })
      setTimeout(() => {
        uni.hideLoading()
        toastManager.success('连接正常')
      }, 1500)
    }

    const initMiniChart = () => {
      // 初始化迷你图表
      const ctx = uni.createCanvasContext('realtimeChart')
      if (!ctx) return

      chartContext.value = ctx
      drawMiniChart()
    }

    const drawMiniChart = () => {
      if (!chartContext.value) return

      const ctx = chartContext.value
      const width = 300
      const height = 120
      const padding = 20
      const chartWidth = width - padding * 2
      const chartHeight = height - padding * 2

      // 清空画布
      ctx.clearRect(0, 0, width, height)

      // 获取最近的数据点
      const recentData = radiationState.history.slice(0, 30).reverse()

      // 如果没有数据，创建模拟数据用于演示
      if (recentData.length < 2) {
        const mockData = []
        for (let i = 0; i < 20; i++) {
          mockData.push({
            doseRate: 0.1 + Math.sin(i * 0.3) * 0.05 + Math.random() * 0.02,
            cps: 50 + Math.sin(i * 0.4) * 15 + Math.random() * 10,
            timestamp: new Date(Date.now() - (19 - i) * 60000)
          })
        }
        recentData.splice(0, 0, ...mockData)
      }

      // 找出最大值和最小值用于缩放
      const doseValues = recentData.map(item => item.doseRate)
      const cpsValues = recentData.map(item => item.cps)
      const maxDose = Math.max(...doseValues)
      const minDose = Math.min(...doseValues)
      const maxCps = Math.max(...cpsValues)
      const minCps = Math.min(...cpsValues)
      const doseRange = maxDose - minDose || 0.1
      const cpsRange = maxCps - minCps || 20

      // 绘制背景网格 - 参考history.html样式
      ctx.setStrokeStyle('rgba(0, 0, 0, 0.05)')
      ctx.setLineWidth(0.5)
      for (let i = 1; i < 5; i++) {
        const y = padding + (i * chartHeight / 5)
        ctx.beginPath()
        ctx.moveTo(padding, y)
        ctx.lineTo(width - padding, y)
        ctx.stroke()
      }

      // 绘制剂量率曲线 - 使用蓝色渐变，参考Chart.js样式
      ctx.beginPath()
      ctx.moveTo(padding, height - padding)

      recentData.forEach((item, index) => {
        const x = padding + (index / (recentData.length - 1)) * chartWidth
        const y = height - padding - ((item.doseRate - minDose) / doseRange) * chartHeight

        if (index === 0) {
          ctx.lineTo(x, y)
        } else {
          // 使用贝塞尔曲线创建平滑效果
          const prevX = padding + ((index - 1) / (recentData.length - 1)) * chartWidth
          const prevY = height - padding - ((recentData[index - 1].doseRate - minDose) / doseRange) * chartHeight
          const cpX = (prevX + x) / 2
          ctx.bezierCurveTo(cpX, prevY, cpX, y, x, y)
        }
      })

      ctx.lineTo(width - padding, height - padding)

      // 创建渐变填充 - 蓝色主题
      const gradient1 = ctx.createLinearGradient(0, padding, 0, height - padding)
      gradient1.addColorStop(0, 'rgba(2, 136, 209, 0.8)')
      gradient1.addColorStop(1, 'rgba(2, 136, 209, 0.1)')
      ctx.setFillStyle(gradient1)
      ctx.fill()

      // 绘制剂量率线条
      ctx.beginPath()
      ctx.setStrokeStyle('#0288D1')
      ctx.setLineWidth(3)
      ctx.setLineCap('round')
      ctx.setLineJoin('round')

      recentData.forEach((item, index) => {
        const x = padding + (index / (recentData.length - 1)) * chartWidth
        const y = height - padding - ((item.doseRate - minDose) / doseRange) * chartHeight

        if (index === 0) {
          ctx.moveTo(x, y)
        } else {
          const prevX = padding + ((index - 1) / (recentData.length - 1)) * chartWidth
          const prevY = height - padding - ((recentData[index - 1].doseRate - minDose) / doseRange) * chartHeight
          const cpX = (prevX + x) / 2
          ctx.bezierCurveTo(cpX, prevY, cpX, y, x, y)
        }
      })
      ctx.stroke()

      // 绘制计数率曲线 - 使用绿色渐变
      ctx.beginPath()
      ctx.moveTo(padding, height - padding)

      recentData.forEach((item, index) => {
        const x = padding + (index / (recentData.length - 1)) * chartWidth
        const y = height - padding - ((item.cps - minCps) / cpsRange) * chartHeight * 0.7

        if (index === 0) {
          ctx.lineTo(x, y)
        } else {
          const prevX = padding + ((index - 1) / (recentData.length - 1)) * chartWidth
          const prevY = height - padding - ((recentData[index - 1].cps - minCps) / cpsRange) * chartHeight * 0.7
          const cpX = (prevX + x) / 2
          ctx.bezierCurveTo(cpX, prevY, cpX, y, x, y)
        }
      })

      ctx.lineTo(width - padding, height - padding)

      // 创建渐变填充 - 绿色主题
      const gradient2 = ctx.createLinearGradient(0, padding, 0, height - padding)
      gradient2.addColorStop(0, 'rgba(76, 175, 80, 0.6)')
      gradient2.addColorStop(1, 'rgba(76, 175, 80, 0.1)')
      ctx.setFillStyle(gradient2)
      ctx.fill()

      // 绘制计数率线条
      ctx.beginPath()
      ctx.setStrokeStyle('#4CAF50')
      ctx.setLineWidth(3)
      ctx.setLineCap('round')
      ctx.setLineJoin('round')

      recentData.forEach((item, index) => {
        const x = padding + (index / (recentData.length - 1)) * chartWidth
        const y = height - padding - ((item.cps - minCps) / cpsRange) * chartHeight * 0.7

        if (index === 0) {
          ctx.moveTo(x, y)
        } else {
          const prevX = padding + ((index - 1) / (recentData.length - 1)) * chartWidth
          const prevY = height - padding - ((recentData[index - 1].cps - minCps) / cpsRange) * chartHeight * 0.7
          const cpX = (prevX + x) / 2
          ctx.bezierCurveTo(cpX, prevY, cpX, y, x, y)
        }
      })
      ctx.stroke()

      // 绘制数据点 - 只显示最后几个点
      const pointsToShow = Math.min(5, recentData.length)
      for (let i = recentData.length - pointsToShow; i < recentData.length; i++) {
        const x = padding + (i / (recentData.length - 1)) * chartWidth
        const doseY = height - padding - ((recentData[i].doseRate - minDose) / doseRange) * chartHeight
        const cpsY = height - padding - ((recentData[i].cps - minCps) / cpsRange) * chartHeight * 0.7

        // 剂量率数据点 - 蓝色
        ctx.beginPath()
        ctx.arc(x, doseY, 4, 0, 2 * Math.PI)
        ctx.setFillStyle('#ffffff')
        ctx.fill()
        ctx.beginPath()
        ctx.arc(x, doseY, 3, 0, 2 * Math.PI)
        ctx.setFillStyle('#0288D1')
        ctx.fill()

        // 计数率数据点 - 绿色
        ctx.beginPath()
        ctx.arc(x, cpsY, 4, 0, 2 * Math.PI)
        ctx.setFillStyle('#ffffff')
        ctx.fill()
        ctx.beginPath()
        ctx.arc(x, cpsY, 3, 0, 2 * Math.PI)
        ctx.setFillStyle('#4CAF50')
        ctx.fill()
      }

      ctx.draw()
    }



    // 生命周期
    onMounted(() => {
      updateTime()
      timeInterval.value = setInterval(updateTime, 1000)

      // 初始化自定义时间
      initCustomTime()

      // 初始化MQTT连接
      mqttService.connect()

      // 注册MQTT事件监听
      mqttService.onMessage('radiationData', (data) => {
        dataStore.updateRadiationData(data)
        drawMiniChart()
      })

      mqttService.onMessage('deviceStatus', (data) => {
        dataStore.updateDeviceStatus(data)
        // 检查设备连接状态
        if (!data.connected) {
          toastManager.warning('设备连接已断开', {
            duration: 4000,
            showCountdown: true
          })
        } else if (deviceState.connection.mqtt === false) {
          toastManager.success('设备连接已恢复', {
            duration: 3000,
            showCountdown: true
          })
        }
      })

      mqttService.onMessage('connected', () => {
        deviceState.connection.mqtt = true
        toastManager.success('MQTT连接成功', {
          duration: 2000,
          showCountdown: false
        })
      })

      mqttService.onMessage('disconnected', () => {
        deviceState.connection.mqtt = false
        toastManager.error('MQTT连接断开', {
          duration: 4000,
          showCountdown: true
        })
      })

      // 初始化图表
      setTimeout(() => {
        initMiniChart()
      }, 500)

      // 模拟数据更新（仅用于演示）
      const simulateData = () => {
        const mockData = {
          doseRate: 0.1 + Math.random() * 0.05,
          cps: 50 + Math.random() * 20,
          doseSum: radiationState.currentData.doseSum + Math.random() * 0.001,
          alarmStatus: Math.random() > 0.95 ? 2 : 0,
          temperature: 25 + Math.random() * 5
        }
        dataStore.updateRadiationData(mockData)
        drawMiniChart()
      }

      // 每5秒更新一次模拟数据
      const dataInterval = setInterval(simulateData, 5000)
      
      onUnmounted(() => {
        clearInterval(dataInterval)
      })
    })

    onUnmounted(() => {
      if (timeInterval.value) {
        clearInterval(timeInterval.value)
      }
      mqttService.disconnect()
    })

    // 计算监测时长
    const getMonitoringDuration = () => {
      const startTime = new Date()
      startTime.setHours(0, 0, 0, 0) // 今天开始时间
      const now = new Date()
      const diffMs = now - startTime
      const hours = Math.floor(diffMs / (1000 * 60 * 60))
      const minutes = Math.floor((diffMs % (1000 * 60 * 60)) / (1000 * 60))
      return `${hours}h ${minutes}m`
    }

         return {
       currentTime,
       isCollecting,
       showConnectionStatus,
       radiationState,
       deviceState,
       locationState,
       radiationLevelClass,
       radiationIcon,
       radiationStatusText,
       todayAvgDoseRate,
       todayMaxDoseRate,
       todayAlerts,
       lastAlert,
       formatDoseRate,
       formatNumber,
       formatPercentage,
       formatTemperature,
       formatCps,
       formatTime,
       toggleCollection,
       exportData,
       showSettings,
       goToCharts,
       hideConnectionStatus,
       getBatteryLevelClass,
       getStatProgress,
       viewDetailedStats,
       viewAlerts,
       refreshChart,
       showQuickMenu,
       reconnectDevice,
       testConnection,
       // 新增的方法和数据
       lastUpdateTime,
       navItems,
       navigateTo,
       navigateToSettings,
       navigateToNotification,
       goToMap,
       refreshData,
       hasUnreadNotifications,
       unreadCount,
       activeTabIndex,
       setActiveTab,
       chartData,
       cpsChartData,
       contentWidth,
       chartHeight,
       isChartLoading: ref(false),
       // 监测相关
       selectedMonitoringPeriod,
       monitoringPeriods,
       monitoringTimeSlots,
       selectMonitoringPeriod,
       viewTimeSlotDetails,
       radiationMatrixCells,
       viewCellDetails,
       // 日历式时间选择
       selectedStartDate,
       selectedEndDate,
       selectedPreset,
       formatDateDisplay,
       dateTimeRange,
       startDateTimeIndex,
       endDateTimeIndex,
       onStartDateTimeChange,
       onEndDateTimeChange,
       updatePickerIndex,
       updateMatrixFromDates,
       updateMatrixDataWithTimeRange,
       customStartTime,
       customEndTime,
       updateCustomTimeRange,
       setPresetTime,
       setPresetTimeWithAnimation,
       triggerDataUpdateAnimation,
       triggerSuccessAnimation,
       triggerErrorAnimation,
       initCustomTime,
       getRadiationProgress,
       dataPointsCount: computed(() => radiationState.history.length),
       currentTrend: computed(() => {
         const recent = radiationState.history.slice(0, 10)
         if (recent.length < 2) return { icon: '➖', text: '无数据', class: 'neutral' }
         const current = recent[0].doseRate
         const previous = recent[recent.length - 1].doseRate
         const change = ((current - previous) / previous) * 100
         if (Math.abs(change) < 1) return { icon: '➖', text: '稳定', class: 'stable' }
         if (change > 0) return { icon: '📈', text: `+${formatNumber(change, 1)}%`, class: 'up' }
         return { icon: '📉', text: `${formatNumber(change, 1)}%`, class: 'down' }
       }),
       getMonitoringDuration
     }
  }
}
</script>

<style scoped>
/* 修复整体样式，确保页面撑满屏幕 - 参考home.html */
page {
    background: linear-gradient(135deg,
        #ffffff 0%,
        #f8fafc 25%,
        #f1f5f9 50%,
        #e2e8f0 75%,
        #f8fafc 100%);
    background-image:
        radial-gradient(circle at 20% 20%, rgba(59, 130, 246, 0.03) 0%, transparent 50%),
        radial-gradient(circle at 80% 80%, rgba(34, 197, 94, 0.03) 0%, transparent 50%),
        radial-gradient(circle at 50% 50%, rgba(255, 152, 0, 0.02) 0%, transparent 70%);
    background-attachment: fixed;
    width: 100%;
    height: 100%;
    overflow-x: hidden;
    font-family: 'Poppins', -apple-system, BlinkMacSystemFont, "SF Pro Text", "Helvetica Neue", Arial, sans-serif;
}

.container {
    width: 100%;
    min-height: 100vh;
    margin: 0;
    padding: 0 0 120px 0; /* 移除左右内边距，让卡片自己控制边距 */
    display: flex;
    flex-direction: column;
    background: transparent; /* 让页面背景透过 */
    position: relative;
    box-sizing: border-box;
}

/* 统一卡片容器样式 - 确保所有卡片左右对齐 */
.card-container {
    width: 100%;
    max-width: 600px;
    margin: 12px auto;
    box-sizing: border-box;
}

/* 现代化头部样式 - 最小化间距 */
.modern-header {
    position: relative;
    width: calc(100% - 32px);
    max-width: 600px;
    padding: 12px 16px;
    margin: 5px auto;
    overflow: hidden;
    box-sizing: border-box;
}

.header-background {
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
}

.header-gradient {
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    background: linear-gradient(135deg,
        rgba(255, 255, 255, 0.95) 0%,
        rgba(248, 250, 252, 0.9) 100%);
    border-radius: 0;
    backdrop-filter: blur(20px);
    -webkit-backdrop-filter: blur(20px);
    border: 1px solid rgba(255, 255, 255, 0.3);
    box-shadow: 0 8px 32px rgba(0, 0, 0, 0.08);
}

.header-pattern {
    position: absolute;
    top: 0;
    right: 0;
    width: 100px;
    height: 100px;
    background: radial-gradient(circle at center,
        rgba(59, 130, 246, 0.1) 0%,
        transparent 70%);
    border-radius: 50%;
    transform: translate(50%, -50%);
}

.header-content {
    position: relative;
    z-index: 1;
    display: flex;
    justify-content: space-between;
    align-items: center;
    padding: 4px;
}

.header-left {
    display: flex;
    align-items: center;
    gap: 16px;
    flex: 1;
}

.app-logo {
    width: 48px;
    height: 48px;
    background: linear-gradient(135deg, #3b82f6 0%, #1d4ed8 100%);
    border-radius: 16px;
    display: flex;
    align-items: center;
    justify-content: center;
    box-shadow: 0 8px 24px rgba(59, 130, 246, 0.3);
}

.app-logo svg {
    width: 24px;
    height: 24px;
    color: #ffffff;
}

.header-info {
    flex: 1;
}

.app-title {
    display: block;
    font-size: 20px;
    font-weight: 700;
    color: #0f172a;
    line-height: 1.3;
}

.app-subtitle {
    display: block;
    font-size: 14px;
    color: #64748b;
    margin-top: 2px;
    font-weight: 500;
}

.header-right {
    display: flex;
    align-items: center;
    gap: 12px;
}

.status-indicators {
    display: flex;
    align-items: center;
    gap: 12px;
}

.connection-status {
    width: 12px;
    height: 12px;
    border-radius: 50%;
    position: relative;
}

.status-dot {
    width: 100%;
    height: 100%;
    border-radius: 50%;
    background: #ef4444;
    transition: all 0.3s ease;
}

.connection-status.connected .status-dot {
    background: linear-gradient(135deg, #22c55e 0%, #16a34a 100%);
    box-shadow:
        0 0 0 3px rgba(34, 197, 94, 0.2),
        0 0 12px rgba(34, 197, 94, 0.3);
    animation: modernStatusGlow 3s ease-in-out infinite;
}

.notification-button {
    position: relative;
    width: 44px;
    height: 44px;
    background: linear-gradient(135deg,
        rgba(248, 250, 252, 0.9) 0%,
        rgba(255, 255, 255, 0.8) 100%);
    border-radius: 16px;
    display: flex;
    align-items: center;
    justify-content: center;
    cursor: pointer;
    transition: all 0.4s cubic-bezier(0.4, 0, 0.2, 1);
    border: 1px solid rgba(255, 255, 255, 0.3);
    backdrop-filter: blur(12px);
}

.notification-button:hover {
    background: linear-gradient(135deg, #3b82f6 0%, #2563eb 100%);
    transform: translateY(-2px) scale(1.05);
    box-shadow: 0 8px 24px rgba(59, 130, 246, 0.3);
}

.notification-button svg {
    width: 22px;
    height: 22px;
    color: #64748b;
    transition: all 0.4s cubic-bezier(0.4, 0, 0.2, 1);
}

.notification-button:hover svg {
    color: #ffffff;
    transform: scale(1.1);
}

.notification-badge {
    position: absolute;
    top: -6px;
    right: -6px;
    background: linear-gradient(135deg, #ef4444 0%, #dc2626 100%);
    color: white;
    font-size: 11px;
    font-weight: 700;
    padding: 4px 8px;
    border-radius: 12px;
    min-width: 20px;
    height: 20px;
    display: flex;
    align-items: center;
    justify-content: center;
    box-shadow:
        0 4px 12px rgba(239, 68, 68, 0.4),
        0 0 0 2px rgba(255, 255, 255, 0.8);
    animation: modernBadgePulse 2s ease-in-out infinite;
}

@keyframes modernStatusGlow {
    0%, 100% {
        opacity: 1;
        transform: scale(1);
        box-shadow:
            0 0 0 3px rgba(34, 197, 94, 0.2),
            0 0 12px rgba(34, 197, 94, 0.3);
    }
    50% {
        opacity: 0.8;
        transform: scale(1.05);
        box-shadow:
            0 0 0 6px rgba(34, 197, 94, 0.1),
            0 0 20px rgba(34, 197, 94, 0.4);
    }
}

@keyframes modernBadgePulse {
    0%, 100% {
        transform: scale(1);
        opacity: 1;
    }
    50% {
        transform: scale(1.1);
        opacity: 0.9;
    }
}

/* 主要辐射数据卡片样式 */
.hero-radiation-card {
    position: relative;
    width: calc(100% - 32px);
    max-width: 480px;
    margin: 0 16px 20px;
    border-radius: 24px;
    overflow: hidden;
    backdrop-filter: blur(20px);
    -webkit-backdrop-filter: blur(20px);
}

.hero-radiation-card .card-background {
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
}

.hero-radiation-card .card-gradient {
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    background: linear-gradient(135deg,
        rgba(59, 130, 246, 0.95) 0%,
        rgba(29, 78, 216, 0.9) 100%);
}

/* 移除水波动效，替换为现代渐变动效 */
.modern-gradient-overlay {
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    background: linear-gradient(
        45deg,
        rgba(59, 130, 246, 0.1) 0%,
        rgba(147, 197, 253, 0.05) 25%,
        rgba(59, 130, 246, 0.08) 50%,
        rgba(147, 197, 253, 0.03) 75%,
        rgba(59, 130, 246, 0.1) 100%
    );
    background-size: 400% 400%;
    animation: modernGradientShift 8s ease-in-out infinite;
    opacity: 0.6;
}

@keyframes modernGradientShift {
    0%, 100% {
        background-position: 0% 50%;
    }
    25% {
        background-position: 100% 50%;
    }
    50% {
        background-position: 100% 100%;
    }
    75% {
        background-position: 0% 100%;
    }
}

.card-content {
    position: relative;
    z-index: 1;
    padding: 28px 24px;
    color: #ffffff;
}

.radiation-header {
    display: flex;
    align-items: center;
    gap: 20px;
    margin-bottom: 24px;
}

.radiation-icon-large {
    width: 64px;
    height: 64px;
    background: rgba(255, 255, 255, 0.2);
    border-radius: 20px;
    display: flex;
    align-items: center;
    justify-content: center;
    backdrop-filter: blur(10px);
    -webkit-backdrop-filter: blur(10px);
    border: 1px solid rgba(255, 255, 255, 0.3);
}

.radiation-icon-large svg {
    width: 32px;
    height: 32px;
    color: #ffffff;
}

.radiation-status {
    flex: 1;
}

.status-title {
    display: block;
    font-size: 18px;
    font-weight: 600;
    color: #0f172a;
    line-height: 1.3;
    margin-bottom: 4px;
}

.status-level {
    display: block;
    font-size: 14px;
    color: rgba(255, 255, 255, 0.8);
    font-weight: 500;
}

.radiation-data {
    display: flex;
    flex-direction: column;
    gap: 20px;
}

.main-reading {
    display: flex;
    align-items: baseline;
    gap: 8px;
}

.reading-value {
    font-size: 48px;
    font-weight: 700;
    color: #0f172a;
    line-height: 1;
}

.reading-unit {
    font-size: 18px;
    color: rgba(255, 255, 255, 0.8);
    font-weight: 500;
}

.secondary-data {
    display: grid;
    grid-template-columns: 1fr 1fr;
    gap: 16px;
}

.data-item {
    background: rgba(255, 255, 255, 0.1);
    border-radius: 16px;
    padding: 16px;
    backdrop-filter: blur(10px);
    -webkit-backdrop-filter: blur(10px);
    border: 1px solid rgba(255, 255, 255, 0.2);
}

.data-label {
    display: block;
    font-size: 12px;
    color: rgba(255, 255, 255, 0.7);
    font-weight: 500;
    margin-bottom: 4px;
}

.data-value {
    display: block;
    font-size: 16px;
    color: #0f172a;
    font-weight: 600;
}



/* 优化实时辐射监测卡片 - 现代玻璃拟态设计 */
.radiation-monitoring-card-redesigned {
  background: linear-gradient(135deg,
    rgba(255, 255, 255, 0.95) 0%,
    rgba(248, 250, 252, 0.9) 50%,
    rgba(255, 255, 255, 0.92) 100%);
  border-radius: 24px;
  margin: 12px 16px;
  padding: 24px;
  width: calc(100% - 32px);
  max-width: 600px;
  box-sizing: border-box;
  box-shadow:
    0 20px 40px rgba(0, 0, 0, 0.08),
    0 8px 16px rgba(0, 0, 0, 0.04),
    inset 0 1px 0 rgba(255, 255, 255, 0.8);
  border: 1px solid rgba(255, 255, 255, 0.4);
  backdrop-filter: blur(24px);
  -webkit-backdrop-filter: blur(24px);
  position: relative;
  overflow: hidden;
  transition: all 0.4s cubic-bezier(0.4, 0, 0.2, 1);
  transform: translateY(0);
}

.radiation-monitoring-card-redesigned:hover {
  transform: translateY(-6px) scale(1.01);
  box-shadow:
    0 24px 48px rgba(0, 0, 0, 0.12),
    0 12px 24px rgba(0, 0, 0, 0.08),
    inset 0 1px 0 rgba(255, 255, 255, 0.9),
    0 0 0 1px rgba(59, 130, 246, 0.1);
}

.radiation-monitoring-card-redesigned:active {
  transform: translateY(-2px) scale(0.99);
  transition: all 0.1s ease;
}



/* 重新设计的卡片头部 - 紧凑设计 */
.card-header-redesigned {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 20px;
  position: relative;
  z-index: 1;
}

.header-left-section {
  display: flex;
  align-items: center;
  gap: 20px;
}

.radiation-icon-large {
  width: 32px;
  height: 32px;
  display: flex;
  align-items: center;
  justify-content: center;
  transition: all 0.3s ease;
}

.radiation-icon-large svg {
  width: 28px;
  height: 28px;
  color: #64748b;
  transition: color 0.3s ease;
}

.radiation-icon-large.safe svg {
  color: #22c55e;
}

.radiation-icon-large.warning svg {
  color: #f59e0b;
}

.radiation-icon-large.danger svg {
  color: #ef4444;
}

.header-text-section {
  display: flex;
  flex-direction: column;
  gap: 4px;
}

.main-title {
  font-size: 28px;
  font-weight: 800;
  color: #0f172a;
  line-height: 1.2;
}

.sub-title {
  font-size: 15px;
  color: #64748b;
  font-weight: 500;
}

.status-indicators-redesigned {
  display: flex;
  gap: 12px;
}

.status-badge {
  padding: 8px 16px;
  border-radius: 20px;
  font-size: 12px;
  font-weight: 600;
  text-transform: uppercase;
  letter-spacing: 0.5px;
  transition: all 0.3s ease;
}

.status-badge.online {
  background: rgba(76, 175, 80, 0.1);
  color: #4CAF50;
  border: 1px solid rgba(76, 175, 80, 0.2);
}

.status-badge.offline {
  background: rgba(244, 67, 54, 0.1);
  color: #F44336;
  border: 1px solid rgba(244, 67, 54, 0.2);
}

.status-badge.monitoring {
  background: rgba(33, 150, 243, 0.1);
  color: #2196F3;
  border: 1px solid rgba(33, 150, 243, 0.2);
}

.status-badge.paused {
  background: rgba(255, 152, 0, 0.1);
  color: #FF9800;
  border: 1px solid rgba(255, 152, 0, 0.2);
}

/* 主要数据显示区域 - 紧凑设计 */
.main-data-section {
  position: relative;
  z-index: 1;
  margin-bottom: 20px;
}

.central-value-display {
  text-align: center;
  margin-bottom: 20px;
  padding: 20px;
  background: rgba(248, 250, 252, 0.6);
  border-radius: 20px;
  border: 1px solid rgba(255, 255, 255, 0.5);
}

.dose-rate-number {
  font-size: 48px;
  font-weight: 900;
  color: #0f172a;
  line-height: 1;
  display: block;
  margin-bottom: 6px;
  transition: color 0.3s ease;
}

/* 辐射水平颜色状态 */
.dose-rate-number.safe {
  color: #22c55e; /* 绿色 - 安全 */
}

.dose-rate-number.warning {
  color: #f59e0b; /* 黄色 - 警告 */
}

.dose-rate-number.danger {
  color: #ef4444; /* 红色 - 超标 */
}

.dose-rate-unit {
  font-size: 20px;
  color: #64748b;
  font-weight: 600;
  display: block;
  margin-bottom: 12px;
}

.status-indicator-bar {
  padding: 12px 24px;
  border-radius: 16px;
  display: inline-block;
  transition: all 0.3s ease;
}

.status-indicator-bar.safe {
  background: rgba(76, 175, 80, 0.1);
  border: 1px solid rgba(76, 175, 80, 0.3);
}

.status-indicator-bar.warning {
  background: rgba(255, 152, 0, 0.1);
  border: 1px solid rgba(255, 152, 0, 0.3);
}

.status-indicator-bar.danger {
  background: rgba(244, 67, 54, 0.1);
  border: 1px solid rgba(244, 67, 54, 0.3);
}

.status-label {
  font-size: 16px;
  font-weight: 700;
  text-transform: uppercase;
  letter-spacing: 1px;
}

.status-indicator-bar.safe .status-label {
  color: #4CAF50;
}

.status-indicator-bar.warning .status-label {
  color: #FF9800;
}

.status-indicator-bar.danger .status-label {
  color: #F44336;
}

/* 信息网格 - 紧凑设计 */
.info-grid-redesigned {
  display: grid;
  grid-template-columns: 1fr;
  gap: 12px;
}

.info-card {
  display: flex;
  align-items: center;
  gap: 12px;
  padding: 16px;
  background: rgba(248, 250, 252, 0.6);
  border-radius: 16px;
  border: 1px solid rgba(226, 232, 240, 0.5);
  transition: all 0.3s ease;
}

.info-card:hover {
  background: rgba(248, 250, 252, 0.8);
  transform: translateY(-2px);
  box-shadow: 0 8px 24px rgba(0, 0, 0, 0.08);
}

.info-icon {
  width: 40px;
  height: 40px;
  border-radius: 12px;
  display: flex;
  align-items: center;
  justify-content: center;
  flex-shrink: 0;
}

.info-icon.shield {
  background: linear-gradient(135deg, #4CAF50 0%, #66BB6A 100%);
}

.info-icon.dollar {
  background: linear-gradient(135deg, #2196F3 0%, #42A5F5 100%);
}

.info-icon.clock {
  background: linear-gradient(135deg, #FF9800 0%, #FFB74D 100%);
}

.info-icon svg {
  width: 24px;
  height: 24px;
  color: #ffffff;
}

.info-content {
  flex: 1;
  display: flex;
  flex-direction: column;
  gap: 4px;
}

.info-label {
  font-size: 14px;
  color: #64748b;
  font-weight: 500;
}

.info-value {
  font-size: 20px;
  color: #0f172a;
  font-weight: 700;
}

/* 底部数据网格 - 紧凑设计 */
.bottom-data-grid {
  display: grid;
  grid-template-columns: repeat(2, 1fr);
  gap: 12px;
  margin-bottom: 16px;
}

.data-item {
  display: flex;
  align-items: center;
  gap: 10px;
  padding: 14px;
  background: rgba(248, 250, 252, 0.6);
  border-radius: 14px;
  border: 1px solid rgba(226, 232, 240, 0.5);
  transition: all 0.3s ease;
}

.data-item:hover {
  background: rgba(248, 250, 252, 0.8);
  transform: translateY(-1px);
  box-shadow: 0 4px 12px rgba(0, 0, 0, 0.05);
}

.data-icon {
  width: 32px;
  height: 32px;
  border-radius: 8px;
  display: flex;
  align-items: center;
  justify-content: center;
  flex-shrink: 0;
}

.data-icon.activity {
  background: linear-gradient(135deg, #3b82f6 0%, #1d4ed8 100%);
}

.data-icon.dollar {
  background: linear-gradient(135deg, #10b981 0%, #059669 100%);
}

.data-icon.thermometer {
  background: linear-gradient(135deg, #f59e0b 0%, #d97706 100%);
}

.data-icon.battery {
  background: linear-gradient(135deg, #8b5cf6 0%, #7c3aed 100%);
}

.data-icon svg {
  width: 16px;
  height: 16px;
  color: #ffffff;
}

.data-details {
  flex: 1;
  display: flex;
  flex-direction: column;
  gap: 2px;
}

.data-value {
  font-size: 16px;
  color: #0f172a;
  font-weight: 700;
}

.data-label {
  font-size: 12px;
  color: #64748b;
  font-weight: 500;
}

/* 操作按钮 - 居中设计 */
.card-actions {
  display: flex;
  gap: 10px;
  justify-content: center;
  align-items: center;
}

.action-btn {
  flex: 1;
  padding: 12px 16px;
  border-radius: 14px;
  text-align: center;
  font-size: 13px;
  font-weight: 600;
  cursor: pointer;
  transition: all 0.3s ease;
  border: none;
}

.action-btn.primary {
  background: linear-gradient(135deg, #3b82f6 0%, #1d4ed8 100%);
  color: white;
  box-shadow: 0 4px 12px rgba(59, 130, 246, 0.3);
}

.action-btn.primary:hover {
  transform: translateY(-2px);
  box-shadow: 0 8px 24px rgba(59, 130, 246, 0.4);
}

.action-btn.secondary {
  background: rgba(248, 250, 252, 0.8);
  color: #374151;
  border: 1px solid rgba(226, 232, 240, 0.5);
}

.action-btn.secondary:hover {
  background: rgba(248, 250, 252, 1);
  transform: translateY(-1px);
  box-shadow: 0 4px 12px rgba(0, 0, 0, 0.05);
}

/* 优化的波形显示容器 - 整体居中缩放设计 */
.charts-container-optimized {
  display: flex;
  flex-direction: column;
  gap: 20px; /* 增大两个波形图之间的间距 */
  margin: 12px 16px;
  width: calc(100% - 32px);
  max-width: 600px;
  box-sizing: border-box;
  transform-origin: center;
  transition: all 0.3s ease;
}

.charts-container-optimized > * {
  width: 100%;
  margin: 0 auto;
  box-sizing: border-box;
  transform-origin: center;
}

/* 辐射监测矩阵样式 - 与其他卡片对齐 */
.radiation-matrix {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(40px, 1fr));
  gap: 3px;
  margin: 12px 16px;
  padding: 12px;
  background-color: rgba(255, 255, 255, 0.95);
  border-radius: 16px;
  border: 2px solid rgba(255, 255, 255, 0.8);
  box-shadow: 0 4px 15px rgba(0, 0, 0, 0.08);
  transition: all 0.3s ease;
  width: calc(100% - 32px);
  max-width: 600px;
  box-sizing: border-box;
  overflow: hidden;
}

.radiation-matrix:hover {
  box-shadow: 0 6px 20px rgba(0, 0, 0, 0.12);
  transform: translateY(-1px);
}

.matrix-cell {
  aspect-ratio: 1/1;
  border-radius: 6px;
  background-color: #f0f0f0;
  display: flex;
  justify-content: center;
  align-items: center;
  font-size: 8px;
  color: #999;
  position: relative;
  cursor: pointer;
  transition: all 0.3s ease;
  border: 1px solid rgba(0, 0, 0, 0.05);
  box-shadow: 0 1px 3px rgba(0, 0, 0, 0.05);
  min-height: 30px;
  max-width: 100%;
}

.matrix-cell:hover {
  transform: scale(1.05);
  box-shadow: 0 4px 8px rgba(0, 0, 0, 0.1);
  z-index: 1;
}

.matrix-cell.safe {
  background-color: #4caf50;
  color: white;
  box-shadow: 0 0 0 1px rgba(76, 175, 80, 0.5), 0 2px 4px rgba(76, 175, 80, 0.2);
}

.matrix-cell.warning {
  background-color: #ff9800;
  color: white;
  box-shadow: 0 0 0 1px rgba(255, 152, 0, 0.5), 0 2px 4px rgba(255, 152, 0, 0.2);
}

.matrix-cell.danger {
  background-color: #f44336;
  color: white;
  box-shadow: 0 0 0 1px rgba(244, 67, 54, 0.5), 0 2px 4px rgba(244, 67, 54, 0.2);
}

.matrix-cell.no-data {
  background-color: #f0f0f0;
  border: 1px solid #ddd;
  color: #aaa;
  cursor: default;
}

.matrix-cell.no-data:hover {
  transform: scale(1.02);
  box-shadow: 0 1px 3px rgba(0, 0, 0, 0.05);
}

.cell-value {
  font-size: 8px;
  font-weight: 600;
  text-align: center;
}

/* 矩阵图例样式 - 与矩阵对齐并适当下移 */
.matrix-legend {
  display: flex;
  justify-content: center;
  gap: 20px;
  margin: 12px 16px;
  max-width: 600px;
  width: calc(100% - 32px);
  flex-wrap: wrap;
  padding: 0 12px;
  box-sizing: border-box;
}

.legend-item {
  display: flex;
  align-items: center;
  gap: 8px;
}

.legend-color {
  width: 12px;
  height: 12px;
  border-radius: 3px;
  border: 1px solid rgba(0, 0, 0, 0.1);
}

.legend-color.safe {
  background-color: #4caf50;
}

.legend-color.warning {
  background-color: #ff9800;
}

.legend-color.danger {
  background-color: #f44336;
}

.legend-color.no-data {
  background-color: #f0f0f0;
}

.legend-text {
  font-size: 12px;
  color: #666;
  font-weight: 500;
}

/* 响应式设计 - 适配不同屏幕尺寸 */
@media screen and (max-width: 375px) {
  /* 小屏幕手机适配 */
  .radiation-monitoring-card-redesigned,
  .device-summary,
  .monitoring-section,
  .section-title,
  .mode-cards-container,
  .radiation-matrix {
    margin: 12px 16px;
    width: calc(100% - 32px);
    max-width: 600px;
  }

  .radiation-monitoring-card-redesigned,
  .monitoring-section {
    padding: 16px;
  }

  .central-value-display {
    padding: 16px;
    margin-bottom: 16px;
  }

  .info-card {
    padding: 12px;
    gap: 10px;
  }

  .data-item {
    padding: 12px;
    gap: 8px;
  }

  .section-title {
    padding: 16px;
  }

  .charts-container-optimized {
    width: calc(100% - 32px);
    max-width: 600px;
    margin: 12px 16px;
  }

  .charts-container-optimized > * {
    width: 100%;
    margin: 0 auto;
  }

  .radiation-matrix {
    grid-template-columns: repeat(6, 1fr);
    gap: 3px;
    padding: 12px;
  }

  .matrix-cell {
    font-size: 8px;
    min-height: 32px;
  }

  .cell-value {
    font-size: 7px;
  }
}

@media screen and (min-width: 768px) {
  /* 平板和大屏幕适配 */
  .container {
    align-items: center;
    max-width: 800px;
    margin: 0 auto;
    padding: 0 0 120px 0;
  }

  .radiation-monitoring-card-redesigned,
  .device-summary,
  .monitoring-section,
  .section-title,
  .mode-cards-container,
  .radiation-matrix,
  .charts-container-optimized,
  .time-filter-container,
  .modern-header {
    max-width: 700px;
    margin-left: auto;
    margin-right: auto;
    width: calc(100% - 64px);
  }

  .charts-container-optimized > * {
    max-width: 100%;
    margin: 0 auto;
    width: 100%;
  }
}

.secondary-data-grid {
  display: grid;
  grid-template-columns: repeat(2, 1fr);
  gap: 12px;
}

.data-cell {
  background: rgba(255, 255, 255, 0.7);
  border-radius: 16px;
  padding: 16px;
  display: flex;
  align-items: center;
  gap: 12px;
  border: 1px solid rgba(255, 255, 255, 0.5);
  transition: all 0.3s ease;
}

.data-cell:hover {
  transform: translateY(-2px);
  box-shadow: 0 8px 24px rgba(0, 0, 0, 0.08);
}

.cell-icon {
  width: 32px;
  height: 32px;
  background: linear-gradient(135deg, #3b82f6 0%, #1d4ed8 100%);
  border-radius: 10px;
  display: flex;
  align-items: center;
  justify-content: center;
  flex-shrink: 0;
}

.cell-icon svg {
  width: 16px;
  height: 16px;
  color: #ffffff;
}

.cell-content {
  flex: 1;
}

.cell-value {
  display: block;
  font-size: 16px;
  font-weight: 700;
  color: #0f172a;
  line-height: 1.2;
}

.cell-label {
  display: block;
  font-size: 12px;
  color: #64748b;
  margin-top: 2px;
  font-weight: 500;
}

.monitoring-actions {
  display: flex;
  gap: 12px;
}

.action-button {
  flex: 1;
  display: flex;
  align-items: center;
  justify-content: center;
  gap: 8px;
  padding: 14px 20px;
  border-radius: 16px;
  font-size: 14px;
  font-weight: 600;
  cursor: pointer;
  transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
  border: none;
}

.action-button.primary {
  background: linear-gradient(135deg, #3b82f6 0%, #1d4ed8 100%);
  color: #ffffff;
  box-shadow: 0 6px 18px rgba(59, 130, 246, 0.3);
}

.action-button.primary:hover {
  transform: translateY(-2px);
  box-shadow: 0 8px 24px rgba(59, 130, 246, 0.4);
}

.action-button.secondary {
  background: rgba(248, 250, 252, 0.8);
  color: #374151;
  border: 1px solid rgba(255, 255, 255, 0.5);
}

.action-button.secondary:hover {
  background: rgba(59, 130, 246, 0.1);
  color: #3b82f6;
  transform: translateY(-1px);
}

.action-button svg {
  width: 16px;
  height: 16px;
}

@keyframes pulse {
  0%, 100% {
    opacity: 1;
  }
  50% {
    opacity: 0.5;
  }
}

/* 全局动画增强 */
@keyframes fadeInUp {
  from {
    opacity: 0;
    transform: translateY(30px);
  }
  to {
    opacity: 1;
    transform: translateY(0);
  }
}

@keyframes slideInLeft {
  from {
    opacity: 0;
    transform: translateX(-30px);
  }
  to {
    opacity: 1;
    transform: translateX(0);
  }
}

@keyframes slideInRight {
  from {
    opacity: 0;
    transform: translateX(30px);
  }
  to {
    opacity: 1;
    transform: translateX(0);
  }
}

/* 为所有卡片添加入场动画 */
.radiation-monitoring-card-redesigned {
  animation: fadeInUp 0.6s ease-out;
}

.device-summary {
  animation: fadeInUp 0.7s ease-out 0.1s both;
}

.charts-container-optimized {
  animation: fadeInUp 0.8s ease-out 0.2s both;
}

.monitoring-section {
  animation: fadeInUp 0.9s ease-out 0.3s both;
}

.section-title {
  animation: fadeInUp 1.0s ease-out 0.4s both;
}

.mode-cards-container {
  animation: fadeInUp 1.1s ease-out 0.5s both;
}

/* SVG图标通用样式 */
.weather-icon svg {
    width: 28px;
    height: 28px;
    color: #f9ad3d;
}

.notification-icon svg {
    width: 20px;
    height: 20px;
    color: white;
}

.device-icon svg {
    width: 28px;
    height: 28px;
    color: #444;
}

.mode-icon svg {
    width: 28px;
    height: 28px;
    color: #444;
}

.nav-icon {
    width: 22px;
    height: 22px;
    margin-bottom: 4px;
    opacity: 0.5;
    transition: all 0.3s ease;
    color: #aaa;
}

/* 顶部状态栏 - 参考home.html设计 */
.status-bar {
    display: flex;
    justify-content: space-between;
    padding: 16px 20px 10px;
    align-items: center;
    position: relative;
    z-index: 10;
    background-color: #f0f0f0;
    width: 100%;
    max-width: 480px;
}

.date-info {
    display: flex;
    flex-direction: column;
}

.date-weather {
    display: flex;
    align-items: center;
    gap: 8px;
    margin-bottom: 4px;
}

.date {
    font-size: 12px;
    color: #999;
}

.weather-icon-small {
    width: 20px;
    height: 20px;
    color: #f9ad3d;
}

.weather-icon-small svg {
    width: 20px;
    height: 20px;
    color: #f9ad3d;
}

.location {
    font-size: 18px;
    font-weight: 600;
    color: #222;
}

.subtitle {
    font-size: 12px;
    color: #666;
    margin-top: 2px;
}

.notification-icon {
    width: 40px;
    height: 40px;
    background-color: #222;
    border-radius: 50%;
    display: flex;
    justify-content: center;
    align-items: center;
    cursor: pointer;
    transition: all 0.3s ease;
}

.notification-icon:hover {
    transform: scale(1.05);
}

.notification-icon svg {
    width: 20px;
    height: 20px;
    color: white;
}

/* 天气信息卡片 */
.weather-card {
    background-color: white;
    border-radius: 16px;
    margin: 0 16px 16px;
    padding: 16px;
    box-shadow: 0 2px 8px rgba(0, 0, 0, 0.05);
    width: calc(100% - 32px);
    max-width: 448px;
}

.weather-card-title {
    font-size: 14px;
    font-weight: 600;
    color: #666;
    margin-bottom: 16px;
}

.weather-info {
    display: flex;
    justify-content: space-between;
    align-items: center;
}

.weather-condition {
    display: flex;
    align-items: center;
}

.weather-icon {
    margin-right: 10px;
    display: flex;
    align-items: center;
}

.weather-data {
    display: flex;
    align-items: center;
}

.weather-temp {
    font-size: 16px;
    font-weight: 600;
    margin: 0 6px;
    color: #333;
}

.temp-divider {
    font-size: 16px;
    color: #ddd;
}

/* 设备状态摘要 - 响应式设计 */
.device-summary {
    display: flex;
    padding: 20px;
    margin: 20px;
    background: linear-gradient(135deg, rgba(255, 255, 255, 0.95) 0%, rgba(248, 250, 252, 0.9) 100%);
    border-radius: 24px;
    box-shadow: 0 4px 15px rgba(0, 0, 0, 0.08);
    justify-content: space-around;
    width: calc(100% - 40px);
    border: 1px solid rgba(255, 255, 255, 0.3);
    backdrop-filter: blur(20px);
    -webkit-backdrop-filter: blur(20px);
    transition: all 0.3s ease;
}

.device-summary:hover {
    transform: translateY(-1px);
    box-shadow: 0 8px 24px rgba(0, 0, 0, 0.12);
}

.summary-item {
    display: flex;
    flex-direction: column;
    align-items: center;
    justify-content: center;
}

.summary-value {
    font-size: 24px;
    font-weight: 700;
    color: #333;
    margin-bottom: 4px;
}

.summary-label {
    font-size: 12px;
    color: #999;
    text-align: center;
}

.summary-circle {
    width: 28px;
    height: 28px;
    border-radius: 50%;
    background-color: #f0f0f0;
    display: flex;
    align-items: center;
    justify-content: center;
    margin-bottom: 4px;
}

.status-dot {
    width: 12px;
    height: 12px;
    border-radius: 50%;
    background-color: #4caf50;
    box-shadow: 0 0 8px rgba(76, 175, 80, 0.6);
}

.status-dot.warning {
    background-color: #ff9800;
    box-shadow: 0 0 8px rgba(255, 152, 0, 0.6);
}

.status-dot.error {
    background-color: #f44336;
    box-shadow: 0 0 8px rgba(244, 67, 54, 0.6);
}

/* 主要内容区域 - 响应式设计 */
.main-content {
    width: 100%;
    flex: 1;
    display: flex;
    flex-direction: column;
}

.last-update-time {
    text-align: center;
    font-size: 12px;
    color: #64748b;
    margin-bottom: 16px;
    padding: 8px 16px;
    background: rgba(255, 255, 255, 0.8);
    border-radius: 20px;
    backdrop-filter: blur(10px);
    -webkit-backdrop-filter: blur(10px);
    border: 1px solid rgba(255, 255, 255, 0.3);
    box-shadow: 0 4px 16px rgba(0, 0, 0, 0.04);
}

/* 修复设备卡片容器和卡片样式 */
.device-cards-container {
    display: grid;
    grid-template-columns: 1fr 1fr;
    gap: 12px;
    margin: 12px 16px;
    padding: 0;
    width: calc(100% - 32px);
    max-width: 600px;
    box-sizing: border-box;
}

.device-card {
    background: linear-gradient(135deg, rgba(255, 255, 255, 0.9) 0%, rgba(248, 250, 252, 0.8) 100%);
    border-radius: 18px;
    overflow: hidden;
    box-shadow: 0 3px 16px rgba(0, 0, 0, 0.04);
    cursor: pointer;
    height: 150px;
    display: flex;
    flex-direction: column;
    position: relative;
    border: 1px solid rgba(255, 255, 255, 0.3);
    transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
    backdrop-filter: blur(10px);
    -webkit-backdrop-filter: blur(10px);
}

.device-card.running {
    border-left: 4px solid #4caf50;
}

.device-card.stopped {
    border-left: 4px solid #f44336;
}

.device-card.sensor {
    border-left: 4px solid #2196f3;
}

.device-card:hover {
    transform: translateY(-4px);
    box-shadow: 0 8px 32px rgba(0, 0, 0, 0.1);
}

.device-card:active {
    transform: translateY(-2px);
    box-shadow: 0 6px 24px rgba(0, 0, 0, 0.12);
}

.device-details {
    padding: 16px;
    flex: 1;
    display: flex;
    flex-direction: column;
    justify-content: space-between;
    height: 100%;
}

.device-icon {
    width: 60px;
    height: 60px;
    border-radius: 16px;
    background: linear-gradient(145deg, #f0f0f0, #fafafa);
    display: flex;
    align-items: center;
    justify-content: center;
    margin-bottom: 12px;
    box-shadow: 0 4px 12px rgba(0, 0, 0, 0.05);
}

.device-icon svg {
    width: 28px;
    height: 28px;
    color: #444;
}

.device-icon.gear svg {
    color: #ff9800;
}

.device-icon.settings svg {
    color: #2196f3;
}

.device-icon.plus svg {
    color: #4caf50;
}

.device-icon.star svg {
    color: #9c27b0;
}

.device-icon.minus svg {
    color: #f44336;
}

.device-icon.refresh svg {
    color: #00bcd4;
}

.device-icon.info svg {
    color: #607d8b;
}

.device-icon.more svg {
    color: #3f51b5;
}

.device-name {
    font-size: 15px;
    font-weight: 600;
    margin-bottom: 8px;
    color: #333;
}

.device-status {
    font-size: 12px;
    font-weight: 500;
    padding: 4px 10px;
    border-radius: 12px;
    background-color: #f0f0f0;
    display: inline-block;
    width: fit-content;
    text-align: center;
    margin: 0 auto 4px;
}

.device-status.running {
    color: #4caf50;
    background-color: rgba(76, 175, 80, 0.1);
    border: 1px solid rgba(76, 175, 80, 0.2);
}

.device-status.stopped {
    color: #f44336;
    background-color: rgba(244, 67, 54, 0.1);
    border: 1px solid rgba(244, 67, 54, 0.2);
}

.device-status.sensor-value {
    color: #2196f3;
    background-color: rgba(33, 150, 243, 0.1);
    border: 1px solid rgba(33, 150, 243, 0.2);
}

.device-update-time {
    font-size: 10px;
    color: #999;
    margin-top: 4px;
    text-align: center;
}

.section-title {
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin: 20px;
    padding: 20px;
    background: linear-gradient(135deg, rgba(255, 255, 255, 0.9) 0%, rgba(248, 250, 252, 0.9) 100%);
    border-radius: 24px;
    box-shadow: 0 4px 20px rgba(0, 0, 0, 0.05);
    border: 1px solid rgba(255, 255, 255, 0.3);
    width: calc(100% - 40px);
    transition: all 0.3s ease;
}

.section-title:hover {
    transform: translateY(-1px);
    box-shadow: 0 6px 28px rgba(0, 0, 0, 0.08);
}

.title-icon {
    width: 32px;
    height: 32px;
    background: linear-gradient(135deg, #3b82f6 0%, #1d4ed8 100%);
    border-radius: 8px;
    display: flex;
    align-items: center;
    justify-content: center;
    margin-right: 12px;
}

.title-icon svg {
    width: 18px;
    height: 18px;
    color: #ffffff;
}

.title-content {
    flex: 1;
}

.title-main {
    display: block;
    font-size: 18px;
    font-weight: 600;
    color: #1e293b;
    line-height: 1.3;
}

.title-sub {
    display: block;
    font-size: 13px;
    color: #64748b;
    margin-top: 2px;
}

.refresh-button {
    width: 40px;
    height: 40px;
    background: rgba(248, 250, 252, 0.8);
    border-radius: 10px;
    display: flex;
    align-items: center;
    justify-content: center;
    cursor: pointer;
    transition: all 0.3s ease;
}

.refresh-button:hover {
    background: #3b82f6;
    transform: scale(1.05);
}

.refresh-button svg {
    width: 20px;
    height: 20px;
    color: #64748b;
    transition: all 0.3s ease;
}

.refresh-button:hover svg {
    color: #ffffff;
}

/* 图表容器 */
.chart-container {
    background: linear-gradient(135deg, rgba(255, 255, 255, 0.95) 0%, rgba(248, 250, 252, 0.95) 100%);
    border-radius: 20px;
    margin-bottom: 24px;
    padding: 24px;
    box-shadow: 0 8px 32px rgba(0, 0, 0, 0.08);
    border: 1px solid rgba(255, 255, 255, 0.3);
    backdrop-filter: blur(10px);
    position: relative;
    overflow: hidden;
}

.chart-container::before {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    height: 3px;
    background: linear-gradient(90deg, #00b4d8 0%, #22c55e 100%);
    border-radius: 20px 20px 0 0;
}

.realtime-chart {
    width: 100%;
    height: 120px;
    background: linear-gradient(135deg, rgba(255, 255, 255, 0.9) 0%, rgba(248, 250, 252, 0.9) 100%);
    border-radius: 12px;
    box-shadow: 0 4px 20px rgba(0, 0, 0, 0.08);
    border: 1px solid rgba(255, 255, 255, 0.2);
}

.chart-legend {
    display: flex;
    justify-content: center;
    align-items: center;
    gap: 24px;
    margin-top: 16px;
    padding-top: 16px;
    border-top: 1px solid rgba(0, 0, 0, 0.05);
}

.legend-item {
    display: flex;
    align-items: center;
    gap: 8px;
}

.legend-dot {
    width: 12px;
    height: 12px;
    border-radius: 50%;
    position: relative;
}

.legend-dot.dose-rate {
    background: linear-gradient(135deg, #00b4d8 0%, #0096c7 100%);
    box-shadow: 0 2px 8px rgba(0, 180, 216, 0.3);
}

.legend-dot.count-rate {
    background: linear-gradient(135deg, #22c55e 0%, #16a34a 100%);
    box-shadow: 0 2px 8px rgba(34, 197, 94, 0.3);
}

.legend-text {
    font-size: 12px;
    color: #64748b;
    font-weight: 500;
}

/* 修复模式卡片容器和卡片样式 - 响应式设计 */
.mode-cards-container {
    display: grid;
    grid-template-columns: 1fr 1fr;
    gap: 12px;
    margin: 12px 16px;
    padding: 0;
    width: calc(100% - 32px);
    max-width: 600px;
    box-sizing: border-box;
}

.mode-card {
    background: linear-gradient(135deg, rgba(255, 255, 255, 0.9) 0%, rgba(248, 250, 252, 0.8) 100%);
    border-radius: 18px;
    padding: 16px 14px;
    height: 110px;
    display: flex;
    flex-direction: column;
    align-items: center;
    justify-content: center;
    box-shadow: 0 3px 16px rgba(0, 0, 0, 0.04);
    cursor: pointer;
    border: 1px solid rgba(255, 255, 255, 0.3);
    transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
    backdrop-filter: blur(10px);
    -webkit-backdrop-filter: blur(10px);
    position: relative;
    overflow: hidden;
}

.mode-card::before {
    content: '';
    position: absolute;
    top: 0;
    right: 0;
    width: 40px;
    height: 40px;
    background: radial-gradient(circle at center,
        rgba(59, 130, 246, 0.1) 0%,
        transparent 70%);
    border-radius: 50%;
    transform: translate(50%, -50%);
}

.mode-card:hover {
    transform: translateY(-4px);
    box-shadow: 0 8px 32px rgba(0, 0, 0, 0.1);
}

.mode-card:active {
    transform: translateY(-2px);
    box-shadow: 0 6px 24px rgba(0, 0, 0, 0.12);
}

.mode-card.active {
    background: linear-gradient(135deg, #3b82f6 0%, #1d4ed8 100%);
    border: 1px solid rgba(59, 130, 246, 0.3);
    box-shadow: 0 8px 32px rgba(59, 130, 246, 0.3);
}

.mode-card.active .mode-icon svg,
.mode-card.active .mode-text {
    color: #ffffff;
}

.mode-icon {
    width: 48px;
    height: 48px;
    border-radius: 12px;
    background-color: #f5f5f5;
    display: flex;
    align-items: center;
    justify-content: center;
    margin-bottom: 12px;
}

.mode-name {
    font-size: 14px;
    font-weight: 600;
    color: #333;
    text-align: center;
}

/* 全新现代底部导航栏 - 参考test文件夹设计 */
.modern-bottom-nav {
    position: fixed;
    bottom: 0;
    left: 0;
    right: 0;
    z-index: 1000;
    padding: 0 20px 20px;
}

.nav-background {
    position: absolute;
    bottom: 0;
    left: 0;
    right: 0;
    height: 100px;
    background: linear-gradient(180deg,
        rgba(255, 255, 255, 0) 0%,
        rgba(255, 255, 255, 0.8) 30%,
        rgba(255, 255, 255, 0.95) 100%);
    backdrop-filter: blur(20px);
    -webkit-backdrop-filter: blur(20px);
}

.nav-content {
    position: relative;
    display: flex;
    justify-content: space-between;
    align-items: center;
    background: rgba(255, 255, 255, 0.9);
    border-radius: 24px;
    padding: 12px 16px;
    margin: 0 8px;
    box-shadow:
        0 8px 32px rgba(0, 0, 0, 0.08),
        0 4px 16px rgba(0, 0, 0, 0.04),
        inset 0 1px 0 rgba(255, 255, 255, 0.8);
    border: 1px solid rgba(255, 255, 255, 0.6);
    backdrop-filter: blur(20px);
    -webkit-backdrop-filter: blur(20px);
}

.nav-tab {
    display: flex;
    flex-direction: column;
    align-items: center;
    padding: 8px 12px;
    border-radius: 16px;
    transition: all 0.4s cubic-bezier(0.4, 0, 0.2, 1);
    cursor: pointer;
    position: relative;
    min-width: 60px;
}

.nav-tab.active {
    background: linear-gradient(135deg, #3b82f6 0%, #1d4ed8 100%);
    box-shadow:
        0 8px 24px rgba(59, 130, 246, 0.3),
        0 4px 12px rgba(59, 130, 246, 0.2);
    transform: translateY(-2px);
}

.nav-tab:hover:not(.active) {
    background: rgba(59, 130, 246, 0.08);
    transform: translateY(-1px);
}

.tab-icon-container {
    position: relative;
    width: 28px;
    height: 28px;
    display: flex;
    align-items: center;
    justify-content: center;
    margin-bottom: 4px;
}

.tab-icon {
    width: 22px;
    height: 22px;
    color: #64748b;
    transition: all 0.3s ease;
}

.nav-tab.active .tab-icon {
    color: #ffffff;
    transform: scale(1.1);
}

.tab-label {
    font-size: 11px;
    font-weight: 500;
    color: #64748b;
    transition: all 0.3s ease;
    text-align: center;
    line-height: 1.2;
}

.nav-tab.active .tab-label {
    color: #ffffff;
    font-weight: 600;
}

.notification-dot {
    position: absolute;
    top: 2px;
    right: 2px;
    width: 8px;
    height: 8px;
    background: linear-gradient(135deg, #ef4444 0%, #dc2626 100%);
    border-radius: 50%;
    border: 2px solid #ffffff;
    box-shadow: 0 2px 8px rgba(239, 68, 68, 0.4);
    animation: pulse-dot 2s infinite;
}

@keyframes pulse-dot {
    0%, 100% { transform: scale(1); opacity: 1; }
    50% { transform: scale(1.2); opacity: 0.8; }
}

/* 监测区域样式 - 完全响应式设计 */
.monitoring-section {
  margin: 12px 16px;
  background: linear-gradient(135deg, rgba(255, 255, 255, 0.95) 0%, rgba(248, 250, 252, 0.9) 100%);
  border-radius: 24px;
  padding: 24px;
  width: calc(100% - 32px);
  max-width: 600px;
  box-sizing: border-box;
  box-shadow:
    0 6px 24px rgba(0, 0, 0, 0.06),
    0 2px 8px rgba(0, 0, 0, 0.03);
  border: 1px solid rgba(255, 255, 255, 0.3);
  backdrop-filter: blur(20px);
  -webkit-backdrop-filter: blur(20px);
  max-width: calc(100vw - 40px);
  width: 100%;
  box-sizing: border-box;
  transition: all 0.3s ease;
}

.monitoring-section:hover {
  transform: translateY(-1px);
  box-shadow:
    0 8px 32px rgba(0, 0, 0, 0.08),
    0 4px 16px rgba(0, 0, 0, 0.04);
}

.section-header-modern {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 20px;
  flex-wrap: wrap;
  gap: 16px;
}

.header-left {
  display: flex;
  align-items: center;
  gap: 16px;
}

.section-icon-wrapper {
  width: 36px;
  height: 36px;
  background: linear-gradient(135deg, #FF9800 0%, #F57C00 100%);
  border-radius: 10px;
  display: flex;
  align-items: center;
  justify-content: center;
  box-shadow: 0 6px 18px rgba(255, 152, 0, 0.25);
}

.section-icon-wrapper svg {
  width: 20px;
  height: 20px;
  color: #ffffff;
}

.section-title-modern {
  font-size: 18px;
  font-weight: 700;
  color: #0f172a;
  display: block;
  line-height: 1.2;
}

.section-subtitle-modern {
  font-size: 13px;
  color: #64748b;
  display: block;
  margin-top: 2px;
  font-weight: 500;
}

/* 时间筛选器容器 - 居中显示 */
/* 日历式时间筛选器容器 */
.time-filter-container {
  display: flex;
  justify-content: center;
  margin: 12px 16px;
  width: calc(100% - 32px);
  max-width: 600px;
  box-sizing: border-box;
}

/* 日历筛选器卡片 */
.calendar-filter-card {
  background: linear-gradient(135deg, rgba(255, 255, 255, 0.95) 0%, rgba(248, 250, 252, 0.9) 100%);
  border-radius: 20px;
  padding: 20px;
  width: 100%;
  box-shadow: 0 8px 32px rgba(0, 0, 0, 0.08);
  border: 1px solid rgba(255, 255, 255, 0.3);
  backdrop-filter: blur(20px);
}

/* 日期输入区域 */
.date-inputs-section {
  display: flex;
  gap: 16px;
  margin-bottom: 16px;
}

.date-input-group {
  flex: 1;
  display: flex;
  flex-direction: column;
  gap: 8px;
}

.date-label {
  font-size: 14px;
  font-weight: 600;
  color: #374151;
  margin-bottom: 4px;
}

.date-input-wrapper {
  display: flex;
  align-items: center;
  justify-content: space-between;
  background: rgba(255, 255, 255, 0.8);
  border: 2px solid rgba(59, 130, 246, 0.2);
  border-radius: 12px;
  padding: 12px 16px;
  cursor: pointer;
  transition: all 0.3s ease;
}

.date-input-wrapper:hover {
  border-color: rgba(59, 130, 246, 0.4);
  background: rgba(255, 255, 255, 0.9);
  transform: translateY(-1px);
}

.date-display {
  font-size: 14px;
  color: #1f2937;
  font-weight: 500;
}

.calendar-icon {
  width: 20px;
  height: 20px;
  color: #6b7280;
  transition: color 0.3s ease;
}

.date-input-wrapper:hover .calendar-icon {
  color: #3b82f6;
}

/* 日期时间选择器样式 */
.datetime-picker {
  width: 100%;
}

.datetime-picker .date-input-wrapper {
  width: 100%;
}

/* 预设按钮区域 */
.preset-buttons {
  display: flex;
  gap: 8px;
  justify-content: center;
}

.preset-btn {
  background: rgba(255, 255, 255, 0.7);
  border: 2px solid rgba(59, 130, 246, 0.2);
  border-radius: 20px;
  padding: 8px 16px;
  font-size: 13px;
  font-weight: 600;
  color: #374151;
  cursor: pointer;
  transition: all 0.3s ease;
  outline: none;
}

.preset-btn:hover {
  background: rgba(59, 130, 246, 0.1);
  border-color: rgba(59, 130, 246, 0.4);
  transform: translateY(-1px);
}

.preset-btn.active {
  background: linear-gradient(135deg, #3b82f6 0%, #1d4ed8 100%);
  border-color: #3b82f6;
  color: white;
  box-shadow: 0 4px 12px rgba(59, 130, 246, 0.3);
}

/* 响应式设计 */
@media (max-width: 480px) {
  .date-inputs-section {
    flex-direction: column;
    gap: 12px;
  }

  .preset-buttons {
    flex-wrap: wrap;
    gap: 6px;
  }

  .preset-btn {
    font-size: 12px;
    padding: 6px 12px;
  }
}

/* 自定义时间选择器样式 - 美化设计 */
.custom-time-picker {
  display: flex;
  flex-direction: column;
  gap: 12px;
  background: linear-gradient(135deg, rgba(255, 255, 255, 0.95) 0%, rgba(248, 250, 252, 0.9) 100%);
  border-radius: 20px;
  padding: 16px;
  border: 1px solid rgba(255, 255, 255, 0.6);
  box-shadow: 0 4px 16px rgba(0, 0, 0, 0.06);
  backdrop-filter: blur(10px);
  -webkit-backdrop-filter: blur(10px);
  max-width: 600px;
  width: 100%;
}

.time-input-group {
  display: flex;
  gap: 12px;
  align-items: flex-end;
  flex-wrap: wrap;
  justify-content: center;
}

.time-input-item {
  display: flex;
  flex-direction: column;
  gap: 6px;
  flex: 1;
  min-width: 160px;
}

.time-label {
  font-size: 12px;
  color: #475569;
  font-weight: 600;
  text-align: center;
}

.time-input {
  padding: 10px 12px;
  border: 1px solid rgba(148, 163, 184, 0.3);
  border-radius: 12px;
  font-size: 12px;
  background: rgba(255, 255, 255, 0.95);
  transition: all 0.3s ease;
  box-sizing: border-box;
  height: 40px;
  text-align: center;
}

.time-input:focus {
  outline: none;
  border-color: #3b82f6;
  box-shadow: 0 0 0 3px rgba(59, 130, 246, 0.1);
  background: rgba(255, 255, 255, 1);
}

.time-actions {
  display: flex;
  gap: 8px;
  flex-wrap: wrap;
  justify-content: center;
}

.time-preset-btn {
  padding: 8px 16px;
  background: linear-gradient(135deg, rgba(59, 130, 246, 0.1) 0%, rgba(99, 102, 241, 0.1) 100%);
  border: 1px solid rgba(59, 130, 246, 0.2);
  border-radius: 16px;
  font-size: 11px;
  color: #3b82f6;
  cursor: pointer;
  transition: all 0.3s ease;
  white-space: nowrap;
  font-weight: 600;
  min-width: 80px;
  text-align: center;
}

.time-preset-btn:hover {
  background: linear-gradient(135deg, rgba(59, 130, 246, 0.15) 0%, rgba(99, 102, 241, 0.15) 100%);
  border-color: #3b82f6;
  color: #1d4ed8;
  transform: translateY(-1px);
  box-shadow: 0 4px 12px rgba(59, 130, 246, 0.25);
}

.time-preset-btn:active {
  background: linear-gradient(135deg, #3b82f6 0%, #1d4ed8 100%);
  border-color: #3b82f6;
  color: white;
  transform: scale(0.98);
}

.monitoring-grid {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(160px, 1fr));
  gap: 12px;
}

.monitoring-card {
  background: rgba(255, 255, 255, 0.8);
  border-radius: 16px;
  padding: 16px;
  border: 2px solid transparent;
  transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
  cursor: pointer;
  position: relative;
  overflow: hidden;
}

.monitoring-card::before {
  content: '';
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  height: 4px;
  border-radius: 16px 16px 0 0;
}

.monitoring-card.normal::before {
  background: linear-gradient(90deg, #4CAF50 0%, #66BB6A 100%);
}

.monitoring-card.warning::before {
  background: linear-gradient(90deg, #FF9800 0%, #FFB74D 100%);
}

.monitoring-card.danger::before {
  background: linear-gradient(90deg, #F44336 0%, #EF5350 100%);
}

.monitoring-card:hover {
  transform: translateY(-2px);
  box-shadow: 0 8px 24px rgba(0, 0, 0, 0.12);
  border-color: rgba(255, 255, 255, 0.5);
}

.time-slot-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 12px;
}

.time-range {
  font-size: 12px;
  font-weight: 600;
  color: #374151;
}

.status-indicator {
  padding: 4px 8px;
  border-radius: 8px;
  font-size: 10px;
  font-weight: 600;
}

.status-indicator.normal {
  background: rgba(76, 175, 80, 0.1);
  color: #4CAF50;
}

.status-indicator.warning {
  background: rgba(255, 152, 0, 0.1);
  color: #FF9800;
}

.status-indicator.danger {
  background: rgba(244, 67, 54, 0.1);
  color: #F44336;
}

.dose-rate-info {
  display: flex;
  align-items: baseline;
  gap: 4px;
  margin-bottom: 12px;
}

.dose-value {
  font-size: 20px;
  font-weight: 700;
  color: #0f172a;
}

.dose-unit {
  font-size: 12px;
  color: #64748b;
  font-weight: 500;
}

.time-slot-stats {
  display: flex;
  justify-content: space-between;
  gap: 8px;
}

.stat-item {
  flex: 1;
  text-align: center;
}

.stat-label {
  display: block;
  font-size: 10px;
  color: #64748b;
  margin-bottom: 2px;
}

.stat-value {
  display: block;
  font-size: 12px;
  font-weight: 600;
  color: #374151;
}





.status-indicator {
    width: 8px;
    height: 8px;
    border-radius: 50%;
    margin-left: 8px;
}

.status-indicator.normal {
    background: linear-gradient(135deg, #22c55e 0%, #16a34a 100%);
    box-shadow: 0 0 0 3px rgba(34, 197, 94, 0.2);
    animation: pulse-status 2s infinite;
}

.status-indicator.warning {
    background: linear-gradient(135deg, #f59e0b 0%, #d97706 100%);
    box-shadow: 0 0 0 3px rgba(245, 158, 11, 0.2);
    animation: pulse-status 1s infinite;
}

.card-content-modern {
    position: relative;
    z-index: 1;
}

.value-display {
    display: flex;
    align-items: baseline;
    margin-bottom: 10px;
}

.main-value {
    font-size: 22px;
    font-weight: 700;
    color: #0f172a;
    line-height: 1;
}

.value-unit {
    font-size: 11px;
    color: #64748b;
    margin-left: 3px;
    font-weight: 500;
}

.trend-indicator {
    display: flex;
    align-items: center;
}

.trend-icon {
    font-size: 12px;
    margin-right: 4px;
}

.trend-text {
    font-size: 10px;
    color: #64748b;
    font-weight: 500;
}

@keyframes pulse-status {
    0%, 100% { opacity: 1; }
    50% { opacity: 0.6; }
}

.nav-item.active .nav-indicator {
    opacity: 1;
    transform: translateX(-50%) scale(1.2);
}

/* 通知徽章 */
.notification-badge {
    position: absolute;
    top: -2px;
    right: -2px;
    background: linear-gradient(135deg, #ef4444 0%, #dc2626 100%);
    color: #ffffff;
    font-size: 10px;
    font-weight: 600;
    padding: 2px 6px;
    border-radius: 10px;
    min-width: 16px;
    height: 16px;
    display: flex;
    align-items: center;
    justify-content: center;
    box-shadow: 0 2px 8px rgba(239, 68, 68, 0.3);
    animation: pulse 2s infinite;
}

@keyframes pulse {
    0%, 100% {
        transform: scale(1);
    }
    50% {
        transform: scale(1.1);
    }
}

/* 动画效果 */
@keyframes fadeIn {
    from { opacity: 0; }
    to { opacity: 1; }
}

@keyframes slideUp {
    from { transform: translateY(10px); opacity: 0; }
    to { transform: translateY(0); opacity: 1; }
}

.animate-fadeIn {
    animation: fadeIn 0.5s ease-in-out;
}

.animate-slideUp {
    animation: slideUp 0.5s ease-in-out;
}

/* 辐射级别样式 - 参考home.html */
.level-safe {
    border-left: 4px solid #4caf50 !important;
}

.level-warning {
    border-left: 4px solid #ff9800 !important;
}

.level-danger {
    border-left: 4px solid #f44336 !important;
}

/* 天气图标样式 */
.weather-icon svg {
    width: 30px;
    height: 30px;
}

.sunny {
    color: #ff9800;
}

.cloudy {
    color: #90a4ae;
}

.rainy {
    color: #64b5f6;
}

.snowy {
    color: #b0bec5;
}

.safe {
    color: #4caf50;
}

.warning {
    color: #ff9800;
}

.danger {
    color: #f44336;
}

/* 修复在小屏手机上的显示 */
@media screen and (max-width: 360px) {
    .device-card {
        width: calc(100% - 8px);
    }

    .mode-card {
        width: calc(100% - 8px);
    }
}

/* 防止在移动端添加额外的外边距或内边距 */
uni-page-body, uni-page {
    margin: 0;
    padding: 0;
    width: 100%;
    height: 100%;
    overflow-x: hidden;
}

/* 删除顶部横线 */

.device-status {
  display: flex;
  align-items: center;
  justify-content: space-between;
  margin-bottom: 24rpx;
}

.status-item {
  display: flex;
  align-items: center;
  gap: 16rpx;
  padding: 16rpx 24rpx;
  background: rgba(248, 250, 252, 0.8);
  border-radius: 20rpx;
  border: 1px solid rgba(226, 232, 240, 0.6);
  transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
}

.status-item.connected {
  background: rgba(16, 185, 129, 0.1);
  border-color: rgba(16, 185, 129, 0.3);
}

.status-icon-wrapper {
  position: relative;
  width: 40rpx;
  height: 40rpx;
  display: flex;
  align-items: center;
  justify-content: center;
}

.status-icon {
  font-size: 24rpx;
  filter: drop-shadow(0 2rpx 4rpx rgba(0, 0, 0, 0.1));
}

.status-pulse {
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  border-radius: 50%;
  background: rgba(16, 185, 129, 0.3);
  animation: statusPulse 2s infinite;
}

@keyframes statusPulse {
  0%, 100% {
    transform: scale(1);
    opacity: 0.7;
  }
  50% {
    transform: scale(1.3);
    opacity: 0.3;
  }
}

.status-text {
  font-size: 24rpx;
  color: #0f172a;
  font-weight: 600;
}

.battery-status {
  display: flex;
  align-items: center;
  gap: 16rpx;
  padding: 16rpx 24rpx;
  background: rgba(248, 250, 252, 0.8);
  border-radius: 20rpx;
  border: 1px solid rgba(226, 232, 240, 0.6);
  position: relative;
  overflow: hidden;
}

.battery-status.high {
  background: rgba(16, 185, 129, 0.1);
  border-color: rgba(16, 185, 129, 0.3);
}

.battery-status.medium {
  background: rgba(245, 158, 11, 0.1);
  border-color: rgba(245, 158, 11, 0.3);
}

.battery-status.low {
  background: rgba(239, 68, 68, 0.1);
  border-color: rgba(239, 68, 68, 0.3);
}

.battery-icon-wrapper {
  position: relative;
  width: 40rpx;
  height: 40rpx;
  display: flex;
  align-items: center;
  justify-content: center;
}

.battery-icon {
  font-size: 24rpx;
  filter: drop-shadow(0 2rpx 4rpx rgba(0, 0, 0, 0.1));
}

.battery-level-indicator {
  position: absolute;
  bottom: 0;
  left: 0;
  height: 100%;
  background: linear-gradient(180deg, #10b981 0%, #059669 100%);
  border-radius: 50%;
  transition: width 0.8s cubic-bezier(0.4, 0, 0.2, 1);
  opacity: 0.6;
}

.battery-text {
  font-size: 24rpx;
  color: #0f172a;
  font-weight: 600;
}

.time-wrapper {
  text-align: center;
}

.current-time {
  font-size: 32rpx;
  color: #0f172a;
  font-weight: 700;
  display: block;
  margin-bottom: 4rpx;
}

.time-label {
  font-size: 20rpx;
  color: #64748b;
  font-weight: 500;
}

/* 超炫主要辐射数据卡片 */
.main-data-card {
  position: relative;
  background: rgba(255, 255, 255, 0.95);
  backdrop-filter: blur(20rpx);
  border-radius: 40rpx;
  padding: 48rpx;
  margin-bottom: 32rpx;
  box-shadow: 0 24rpx 48rpx rgba(0, 0, 0, 0.08);
  border: 1px solid rgba(255, 255, 255, 0.6);
  overflow: hidden;
  transition: all 0.4s cubic-bezier(0.4, 0, 0.2, 1);
}

.main-data-card.level-safe {
  background: linear-gradient(135deg, rgba(16, 185, 129, 0.08) 0%, rgba(255, 255, 255, 0.95) 100%);
  border-color: rgba(16, 185, 129, 0.3);
  box-shadow: 0 24rpx 48rpx rgba(16, 185, 129, 0.12);
}

.main-data-card.level-warning {
  background: linear-gradient(135deg, rgba(245, 158, 11, 0.08) 0%, rgba(255, 255, 255, 0.95) 100%);
  border-color: rgba(245, 158, 11, 0.3);
  box-shadow: 0 24rpx 48rpx rgba(245, 158, 11, 0.12);
}

.main-data-card.level-danger {
  background: linear-gradient(135deg, rgba(239, 68, 68, 0.08) 0%, rgba(255, 255, 255, 0.95) 100%);
  border-color: rgba(239, 68, 68, 0.4);
  box-shadow: 0 24rpx 48rpx rgba(239, 68, 68, 0.15);
  animation: dangerGlow 3s infinite;
}

@keyframes dangerGlow {
  0%, 100% {
    box-shadow: 0 24rpx 48rpx rgba(239, 68, 68, 0.15);
  }
  50% {
    box-shadow: 0 32rpx 64rpx rgba(239, 68, 68, 0.25);
  }
}

.card-bg-pattern {
  position: absolute;
  top: -50%;
  right: -50%;
  width: 200%;
  height: 200%;
  background: radial-gradient(circle, rgba(255, 255, 255, 0.1) 0%, transparent 70%);
  animation: patternRotate 20s linear infinite;
  pointer-events: none;
}

@keyframes patternRotate {
  from {
    transform: rotate(0deg);
  }
  to {
    transform: rotate(360deg);
  }
}

.radiation-header {
  display: flex;
  align-items: center;
  justify-content: space-between;
  margin-bottom: 36rpx;
  position: relative;
  z-index: 2;
}

.status-indicator {
  position: relative;
  width: 100rpx;
  height: 100rpx;
  border-radius: 50%;
  display: flex;
  align-items: center;
  justify-content: center;
  background: linear-gradient(135deg, #ffffff 0%, #f8fafc 100%);
  box-shadow: 0 12rpx 24rpx rgba(0, 0, 0, 0.1);
}

.indicator-icon {
  font-size: 48rpx;
  filter: drop-shadow(0 2rpx 4rpx rgba(0, 0, 0, 0.1));
  position: relative;
  z-index: 3;
}

.indicator-glow {
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  border-radius: 50%;
  opacity: 0.6;
  animation: indicatorPulse 3s infinite;
}

.indicator-glow.level-safe {
  background: radial-gradient(circle, rgba(16, 185, 129, 0.3) 0%, transparent 70%);
}

.indicator-glow.level-warning {
  background: radial-gradient(circle, rgba(245, 158, 11, 0.3) 0%, transparent 70%);
}

.indicator-glow.level-danger {
  background: radial-gradient(circle, rgba(239, 68, 68, 0.3) 0%, transparent 70%);
}

@keyframes indicatorPulse {
  0%, 100% {
    transform: scale(1);
    opacity: 0.6;
  }
  50% {
    transform: scale(1.2);
    opacity: 0.3;
  }
}

.indicator-pulse {
  position: absolute;
  top: -10rpx;
  left: -10rpx;
  right: -10rpx;
  bottom: -10rpx;
  border-radius: 50%;
  border: 2rpx solid;
  animation: ripple 2s infinite;
}

.indicator-pulse.level-safe {
  border-color: rgba(16, 185, 129, 0.4);
}

.indicator-pulse.level-warning {
  border-color: rgba(245, 158, 11, 0.4);
}

.indicator-pulse.level-danger {
  border-color: rgba(239, 68, 68, 0.4);
}

@keyframes ripple {
  0% {
    transform: scale(1);
    opacity: 1;
  }
  100% {
    transform: scale(1.3);
    opacity: 0;
  }
}

.title-section {
  flex: 1;
  margin-left: 24rpx;
}

.radiation-title {
  font-size: 32rpx;
  color: #0f172a;
  font-weight: 700;
  margin-bottom: 8rpx;
  display: block;
}

.radiation-subtitle {
  font-size: 24rpx;
  color: #64748b;
  font-weight: 500;
  display: block;
}

.status-badge {
  padding: 12rpx 20rpx;
  border-radius: 20rpx;
  backdrop-filter: blur(10rpx);
  border: 1px solid rgba(255, 255, 255, 0.5);
}

.status-badge.level-safe {
  background: rgba(16, 185, 129, 0.15);
  border-color: rgba(16, 185, 129, 0.3);
}

.status-badge.level-warning {
  background: rgba(245, 158, 11, 0.15);
  border-color: rgba(245, 158, 11, 0.3);
}

.status-badge.level-danger {
  background: rgba(239, 68, 68, 0.15);
  border-color: rgba(239, 68, 68, 0.3);
}

.badge-text {
  font-size: 20rpx;
  font-weight: 600;
  color: #0f172a;
}

.radiation-main {
  text-align: center;
  margin-bottom: 40rpx;
  position: relative;
  z-index: 2;
}

.dose-rate-display {
  display: flex;
  flex-direction: column;
  align-items: center;
  gap: 16rpx;
}

.value-container {
  position: relative;
  display: inline-block;
}

.dose-value {
  font-size: 80rpx;
  font-weight: 800;
  color: #0f172a;
  line-height: 1;
  text-shadow: 0 4rpx 8rpx rgba(0, 0, 0, 0.1);
  position: relative;
  z-index: 2;
}

.value-glow {
  position: absolute;
  top: 50%;
  left: 50%;
  transform: translate(-50%, -50%);
  width: 120%;
  height: 120%;
  border-radius: 50%;
  opacity: 0.3;
  animation: valueGlow 4s infinite;
}

.value-glow.level-safe {
  background: radial-gradient(circle, rgba(16, 185, 129, 0.4) 0%, transparent 70%);
}

.value-glow.level-warning {
  background: radial-gradient(circle, rgba(245, 158, 11, 0.4) 0%, transparent 70%);
}

.value-glow.level-danger {
  background: radial-gradient(circle, rgba(239, 68, 68, 0.4) 0%, transparent 70%);
}

@keyframes valueGlow {
  0%, 100% {
    transform: translate(-50%, -50%) scale(1);
    opacity: 0.3;
}
  50% {
    transform: translate(-50%, -50%) scale(1.1);
    opacity: 0.5;
  }
}

.dose-unit {
  font-size: 28rpx;
  color: #64748b;
  font-weight: 600;
  margin-top: 8rpx;
}

.trend-indicator {
  display: flex;
  align-items: center;
  gap: 8rpx;
  padding: 12rpx 20rpx;
  background: rgba(255, 255, 255, 0.8);
  border-radius: 20rpx;
  border: 1px solid rgba(226, 232, 240, 0.5);
  margin-top: 16rpx;
}

.trend-icon {
  font-size: 24rpx;
}

.trend-text {
  font-size: 20rpx;
  font-weight: 600;
  color: #0f172a;
}

.trend-indicator.up {
  background: rgba(16, 185, 129, 0.1);
  border-color: rgba(16, 185, 129, 0.3);
}

.trend-indicator.up .trend-text {
  color: #10b981;
}

.trend-indicator.down {
  background: rgba(239, 68, 68, 0.1);
  border-color: rgba(239, 68, 68, 0.3);
}

.trend-indicator.down .trend-text {
  color: #ef4444;
}

.trend-indicator.stable {
  background: rgba(156, 163, 175, 0.1);
  border-color: rgba(156, 163, 175, 0.3);
}

.trend-indicator.stable .trend-text {
  color: #6b7280;
}

.additional-data {
  position: relative;
  z-index: 2;
}

.data-grid {
  display: grid;
  grid-template-columns: repeat(3, 1fr);
  gap: 20rpx;
}

.data-item {
  background: rgba(255, 255, 255, 0.7);
  backdrop-filter: blur(10rpx);
  border-radius: 24rpx;
  padding: 24rpx 20rpx;
  text-align: center;
  border: 1px solid rgba(255, 255, 255, 0.5);
  transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
  position: relative;
  overflow: hidden;
}

.data-item:hover {
  transform: translateY(-4rpx);
  box-shadow: 0 12rpx 24rpx rgba(0, 0, 0, 0.1);
}

.data-item.primary {
  background: linear-gradient(135deg, rgba(0, 180, 216, 0.1) 0%, rgba(255, 255, 255, 0.8) 100%);
}

.data-item.secondary {
  background: linear-gradient(135deg, rgba(16, 185, 129, 0.1) 0%, rgba(255, 255, 255, 0.8) 100%);
}

.data-item.tertiary {
  background: linear-gradient(135deg, rgba(245, 158, 11, 0.1) 0%, rgba(255, 255, 255, 0.8) 100%);
}

.data-icon-wrapper {
  position: relative;
  width: 60rpx;
  height: 60rpx;
  margin: 0 auto 16rpx;
  display: flex;
  align-items: center;
  justify-content: center;
  border-radius: 50%;
  background: linear-gradient(135deg, #ffffff 0%, #f8fafc 100%);
  box-shadow: 0 8rpx 16rpx rgba(0, 0, 0, 0.08);
}

.data-icon {
  font-size: 32rpx;
  filter: drop-shadow(0 2rpx 4rpx rgba(0, 0, 0, 0.1));
}

.icon-bg {
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  border-radius: 50%;
  background: radial-gradient(circle, rgba(0, 180, 216, 0.1) 0%, transparent 70%);
  opacity: 0;
  transition: opacity 0.3s ease;
}

.data-item:hover .icon-bg {
  opacity: 1;
}

.data-content {
  text-align: center;
}

.data-label {
  font-size: 20rpx;
  color: #64748b;
  font-weight: 500;
  margin-bottom: 8rpx;
  display: block;
}

.data-value {
  font-size: 32rpx;
  color: #0f172a;
  font-weight: 700;
  line-height: 1.2;
  margin-bottom: 4rpx;
  display: block;
}

.data-unit {
  font-size: 18rpx;
  color: #94a3b8;
  font-weight: 500;
  display: block;
}

.card-shine {
  position: absolute;
  top: 0;
  left: -100%;
  width: 100%;
  height: 100%;
  background: linear-gradient(90deg, transparent, rgba(255, 255, 255, 0.4), transparent);
  animation: cardShine 6s infinite;
  pointer-events: none;
}

@keyframes cardShine {
  0%, 90%, 100% {
    left: -100%;
  }
  10%, 80% {
    left: 100%;
  }
}

/* 精美的快速数据概览 */
.quick-overview {
  display: grid;
  grid-template-columns: repeat(2, 1fr);
  gap: 24rpx;
  margin-bottom: 32rpx;
}

.overview-card {
  position: relative;
  background: rgba(255, 255, 255, 0.95);
  backdrop-filter: blur(20rpx);
  border-radius: 32rpx;
  padding: 32rpx;
  box-shadow: 0 16rpx 32rpx rgba(0, 0, 0, 0.08);
  border: 1px solid rgba(255, 255, 255, 0.6);
  transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
  overflow: hidden;
  cursor: pointer;
}

.overview-card:hover {
  transform: translateY(-8rpx);
  box-shadow: 0 24rpx 48rpx rgba(0, 0, 0, 0.12);
}

.overview-card.stats-card {
  background: linear-gradient(135deg, rgba(0, 180, 216, 0.05) 0%, rgba(255, 255, 255, 0.95) 100%);
}

.overview-card.alert-card {
  background: linear-gradient(135deg, rgba(245, 158, 11, 0.05) 0%, rgba(255, 255, 255, 0.95) 100%);
}

.overview-card.alert-card.has-alerts {
  background: linear-gradient(135deg, rgba(239, 68, 68, 0.08) 0%, rgba(255, 255, 255, 0.95) 100%);
  border-color: rgba(239, 68, 68, 0.3);
}

.card-bg-effect {
  position: absolute;
  top: -50%;
  right: -50%;
  width: 100%;
  height: 100%;
  background: radial-gradient(circle, rgba(255, 255, 255, 0.1) 0%, transparent 70%);
  transition: transform 0.6s ease;
  pointer-events: none;
}

.overview-card:hover .card-bg-effect {
  transform: scale(1.5) rotate(30deg);
}

.card-header {
  display: flex;
  align-items: center;
  justify-content: space-between;
  margin-bottom: 24rpx;
}

.header-icon-wrapper {
  position: relative;
  width: 60rpx;
  height: 60rpx;
  display: flex;
  align-items: center;
  justify-content: center;
  background: linear-gradient(135deg, #ffffff 0%, #f8fafc 100%);
  border-radius: 50%;
  box-shadow: 0 8rpx 16rpx rgba(0, 0, 0, 0.08);
}

.card-icon {
  font-size: 32rpx;
  filter: drop-shadow(0 2rpx 4rpx rgba(0, 0, 0, 0.1));
}

.icon-pulse {
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  border-radius: 50%;
  background: rgba(0, 180, 216, 0.1);
  animation: iconPulse 2s infinite;
}

@keyframes iconPulse {
  0%, 100% {
    transform: scale(1);
    opacity: 0.6;
  }
  50% {
    transform: scale(1.2);
    opacity: 0.3;
  }
}

.alert-indicator {
  position: absolute;
  top: -4rpx;
  right: -4rpx;
  width: 20rpx;
  height: 20rpx;
  background: #ef4444;
  border-radius: 50%;
  border: 3rpx solid #ffffff;
  animation: alertBlink 1.5s infinite;
}

@keyframes alertBlink {
  0%, 100% {
    opacity: 1;
  }
  50% {
    opacity: 0.5;
  }
}

.header-content {
  flex: 1;
  margin-left: 16rpx;
}

.card-title {
  font-size: 28rpx;
  color: #0f172a;
  font-weight: 700;
  margin-bottom: 4rpx;
  display: block;
}

.card-subtitle {
  font-size: 20rpx;
  color: #64748b;
  font-weight: 500;
  display: block;
}

.header-action {
  padding: 12rpx;
  background: rgba(0, 180, 216, 0.1);
  border-radius: 50%;
  transition: all 0.3s ease;
}

.action-arrow {
  font-size: 24rpx;
  color: #00b4d8;
  font-weight: bold;
  transition: transform 0.3s ease;
}

.overview-card:hover .action-arrow {
  transform: translateX(4rpx);
}

.alert-badge {
  background: #ef4444;
  color: #ffffff;
  padding: 8rpx 12rpx;
  border-radius: 50%;
  min-width: 40rpx;
  text-align: center;
}

.badge-count {
  font-size: 20rpx;
  font-weight: 700;
}

.card-content {
  margin-bottom: 16rpx;
}

.stats-grid {
  display: grid;
  gap: 16rpx;
}

.stat-item {
  display: flex;
  flex-direction: column;
  gap: 8rpx;
}

.stat-label {
  font-size: 20rpx;
  color: #64748b;
  font-weight: 500;
}

.stat-value {
  font-size: 28rpx;
  color: #0f172a;
  font-weight: 700;
  line-height: 1.2;
}

.stat-unit {
  font-size: 18rpx;
  color: #94a3b8;
  font-weight: 500;
}

.stat-bar {
  height: 6rpx;
  background: rgba(226, 232, 240, 0.8);
  border-radius: 3rpx;
  overflow: hidden;
  margin-top: 4rpx;
}

.bar-fill {
  height: 100%;
  background: linear-gradient(90deg, #00b4d8 0%, #0096c7 100%);
  border-radius: 3rpx;
  transition: width 0.8s cubic-bezier(0.4, 0, 0.2, 1);
}

.card-footer {
  padding-top: 16rpx;
  border-top: 1px solid rgba(226, 232, 240, 0.5);
  text-align: center;
}

.footer-text {
  font-size: 18rpx;
  color: #94a3b8;
  font-weight: 500;
}

.alert-summary {
  display: flex;
  align-items: center;
  justify-content: space-between;
  margin-bottom: 16rpx;
}

.alert-count-display {
  text-align: center;
}

.count-number {
  font-size: 48rpx;
  color: #0f172a;
  font-weight: 800;
  line-height: 1;
  display: block;
}

.count-label {
  font-size: 20rpx;
  color: #64748b;
  font-weight: 500;
  margin-top: 4rpx;
  display: block;
}

.alert-status {
  padding: 12rpx 20rpx;
  background: rgba(156, 163, 175, 0.1);
  border-radius: 20rpx;
  border: 1px solid rgba(156, 163, 175, 0.3);
}

.alert-status.active {
  background: rgba(239, 68, 68, 0.1);
  border-color: rgba(239, 68, 68, 0.3);
}

.alert-status .status-text {
  font-size: 20rpx;
  font-weight: 600;
  color: #6b7280;
}

.alert-status.active .status-text {
  color: #ef4444;
}

.last-alert {
  display: flex;
  align-items: flex-start;
  gap: 16rpx;
  padding: 16rpx;
  background: rgba(248, 250, 252, 0.8);
  border-radius: 16rpx;
  border: 1px solid rgba(226, 232, 240, 0.6);
}

.alert-timeline {
  display: flex;
  flex-direction: column;
  align-items: center;
  gap: 8rpx;
}

.timeline-dot {
  width: 12rpx;
  height: 12rpx;
  background: #ef4444;
  border-radius: 50%;
  box-shadow: 0 0 0 4rpx rgba(239, 68, 68, 0.2);
}

.timeline-line {
  width: 2rpx;
  height: 20rpx;
  background: rgba(239, 68, 68, 0.3);
}

.alert-info {
  flex: 1;
}

.alert-time {
  font-size: 18rpx;
  color: #94a3b8;
  font-weight: 500;
  margin-bottom: 4rpx;
  display: block;
}

.alert-msg {
  font-size: 20rpx;
  color: #0f172a;
  font-weight: 600;
  line-height: 1.3;
}

/* 炫酷的实时图表预览 */
.chart-preview {
  margin-bottom: 32rpx;
}

.chart-wrapper {
  background: rgba(255, 255, 255, 0.95);
  backdrop-filter: blur(20rpx);
  border-radius: 32rpx;
  padding: 32rpx;
  box-shadow: 0 16rpx 32rpx rgba(0, 0, 0, 0.08);
  border: 1px solid rgba(255, 255, 255, 0.6);
}

.chart-header {
  display: flex;
  align-items: center;
  justify-content: space-between;
  margin-bottom: 24rpx;
}

.chart-title-section {
  flex: 1;
}

.chart-title {
  font-size: 28rpx;
  color: #0f172a;
  font-weight: 700;
  margin-bottom: 4rpx;
  display: block;
}

.chart-subtitle {
  font-size: 20rpx;
  color: #64748b;
  font-weight: 500;
  display: block;
}

.chart-actions {
  display: flex;
  gap: 12rpx;
}

.action-btn {
  display: flex;
  align-items: center;
  gap: 8rpx;
  padding: 12rpx 16rpx;
  background: rgba(248, 250, 252, 0.8);
  border-radius: 16rpx;
  border: 1px solid rgba(226, 232, 240, 0.6);
  transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
  cursor: pointer;
}

.action-btn:hover {
  background: rgba(0, 180, 216, 0.1);
  border-color: rgba(0, 180, 216, 0.3);
}

.action-btn.primary {
  background: linear-gradient(135deg, #00b4d8 0%, #0096c7 100%);
  border-color: rgba(0, 180, 216, 0.3);
}

.action-btn.primary:hover {
  transform: translateY(-2rpx) scale(1.05);
  box-shadow: 0 8rpx 16rpx rgba(0, 180, 216, 0.3);
}

.btn-icon {
  font-size: 20rpx;
}

.btn-text {
  font-size: 20rpx;
  font-weight: 600;
  color: #0f172a;
}

.action-btn.primary .btn-text,
.action-btn.primary .btn-arrow {
  color: #ffffff;
}

.btn-arrow {
  font-size: 18rpx;
  font-weight: bold;
  transition: transform 0.3s ease;
}

.action-btn:hover .btn-arrow {
  transform: translateX(2rpx);
}

.mini-chart-container {
  position: relative;
  background: linear-gradient(135deg, #fefefe 0%, #f8fafc 100%);
  border-radius: 20rpx;
  padding: 24rpx;
  border: 1px solid rgba(226, 232, 240, 0.5);
  box-shadow: inset 0 2rpx 8rpx rgba(0, 0, 0, 0.05);
}

.chart-canvas {
  width: 100%;
  height: 200rpx;
  border-radius: 12rpx;
}

.chart-overlay {
  position: absolute;
  top: 24rpx;
  left: 24rpx;
  right: 24rpx;
  bottom: 56rpx;
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  background: rgba(255, 255, 255, 0.95);
  backdrop-filter: blur(4rpx);
  border-radius: 12rpx;
  gap: 16rpx;
}

.loading-spinner {
  width: 40rpx;
  height: 40rpx;
  border: 4rpx solid rgba(0, 180, 216, 0.2);
  border-left: 4rpx solid #00b4d8;
  border-radius: 50%;
  animation: spin 1s linear infinite;
}

@keyframes spin {
  to {
    transform: rotate(360deg);
  }
}

.loading-text {
  font-size: 20rpx;
  color: #64748b;
  font-weight: 500;
}

.chart-legend {
  display: flex;
  justify-content: center;
  gap: 32rpx;
  margin-top: 16rpx;
  padding-top: 16rpx;
  border-top: 1px solid rgba(226, 232, 240, 0.5);
}

.legend-item {
  display: flex;
  align-items: center;
  gap: 8rpx;
}

.legend-dot {
  width: 12rpx;
  height: 12rpx;
  border-radius: 50%;
  box-shadow: 0 2rpx 4rpx rgba(0, 0, 0, 0.1);
}

.legend-dot.primary {
  background: linear-gradient(135deg, #00b4d8 0%, #0096c7 100%);
}

.legend-dot.secondary {
  background: linear-gradient(135deg, #10b981 0%, #059669 100%);
}

.legend-text {
  font-size: 20rpx;
  color: #64748b;
  font-weight: 500;
}

/* 现代化快速操作按钮 */
.quick-actions {
  margin-bottom: 32rpx;
}

.actions-header {
  text-align: center;
  margin-bottom: 24rpx;
}

.actions-title {
  font-size: 32rpx;
  color: #0f172a;
  font-weight: 700;
  margin-bottom: 8rpx;
  display: block;
}

.actions-subtitle {
  font-size: 24rpx;
  color: #64748b;
  font-weight: 500;
  display: block;
}

.actions-grid {
  display: grid;
  gap: 20rpx;
}

.action-button {
  position: relative;
  background: rgba(255, 255, 255, 0.95);
  backdrop-filter: blur(20rpx);
  border-radius: 28rpx;
  padding: 32rpx 24rpx;
  box-shadow: 0 12rpx 24rpx rgba(0, 0, 0, 0.08);
  border: 1px solid rgba(255, 255, 255, 0.6);
  transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
  cursor: pointer;
  overflow: hidden;
  display: flex;
  align-items: center;
  gap: 24rpx;
}

.action-button:hover {
  transform: translateY(-6rpx);
  box-shadow: 0 20rpx 40rpx rgba(0, 0, 0, 0.12);
}

.action-button.primary {
  background: linear-gradient(135deg, rgba(0, 180, 216, 0.08) 0%, rgba(255, 255, 255, 0.95) 100%);
}

.action-button.primary.active {
  background: linear-gradient(135deg, rgba(0, 180, 216, 0.15) 0%, rgba(255, 255, 255, 0.95) 100%);
  border-color: rgba(0, 180, 216, 0.3);
}

.action-button.secondary {
  background: linear-gradient(135deg, rgba(16, 185, 129, 0.08) 0%, rgba(255, 255, 255, 0.95) 100%);
}

.action-button.tertiary {
  background: linear-gradient(135deg, rgba(245, 158, 11, 0.08) 0%, rgba(255, 255, 255, 0.95) 100%);
}

.action-icon-wrapper {
  position: relative;
  width: 80rpx;
  height: 80rpx;
  display: flex;
  align-items: center;
  justify-content: center;
  background: linear-gradient(135deg, #ffffff 0%, #f8fafc 100%);
  border-radius: 50%;
  box-shadow: 0 8rpx 16rpx rgba(0, 0, 0, 0.08);
  overflow: hidden;
}

.action-icon {
  font-size: 40rpx;
  filter: drop-shadow(0 2rpx 4rpx rgba(0, 0, 0, 0.1));
  position: relative;
  z-index: 2;
}

.icon-ripple {
  position: absolute;
  top: 50%;
  left: 50%;
  width: 0;
  height: 0;
  border-radius: 50%;
  background: rgba(0, 180, 216, 0.3);
  transform: translate(-50%, -50%);
  transition: width 0.3s ease, height 0.3s ease;
}

.action-button:active .icon-ripple {
  width: 100rpx;
  height: 100rpx;
}

.action-content {
  flex: 1;
}

.action-text {
  font-size: 28rpx;
  color: #0f172a;
  font-weight: 700;
  margin-bottom: 8rpx;
  display: block;
}

.action-desc {
  font-size: 20rpx;
  color: #64748b;
  font-weight: 500;
  display: block;
}

.action-status {
  width: 16rpx;
  height: 16rpx;
  border-radius: 50%;
  background: rgba(156, 163, 175, 0.5);
  transition: all 0.3s ease;
}

.action-status.active {
  background: #10b981;
  box-shadow: 0 0 0 4rpx rgba(16, 185, 129, 0.2);
  animation: statusPulse 2s infinite;
}

/* 炫酷连接状态浮窗 */
.connection-overlay {
  position: fixed;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  z-index: 1000;
  display: flex;
  align-items: center;
  justify-content: center;
  padding: 40rpx;
}

.overlay-backdrop {
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background: rgba(0, 0, 0, 0.5);
  backdrop-filter: blur(4rpx);
}

.overlay-content {
  position: relative;
  background: rgba(255, 255, 255, 0.95);
  backdrop-filter: blur(20rpx);
  border-radius: 32rpx;
  padding: 40rpx;
  box-shadow: 0 32rpx 64rpx rgba(0, 0, 0, 0.2);
  border: 1px solid rgba(255, 255, 255, 0.6);
  max-width: 600rpx;
  width: 100%;
  max-height: 80vh;
  overflow-y: auto;
}

.overlay-header {
  display: flex;
  align-items: center;
  justify-content: space-between;
  margin-bottom: 32rpx;
  padding-bottom: 20rpx;
  border-bottom: 1px solid rgba(226, 232, 240, 0.5);
}

.overlay-title {
  font-size: 32rpx;
  color: #0f172a;
  font-weight: 700;
}

.close-btn {
  width: 48rpx;
  height: 48rpx;
  display: flex;
  align-items: center;
  justify-content: center;
  background: rgba(248, 250, 252, 0.8);
  border-radius: 50%;
  border: 1px solid rgba(226, 232, 240, 0.6);
  transition: all 0.3s ease;
  cursor: pointer;
}

.close-btn:hover {
  background: rgba(239, 68, 68, 0.1);
  border-color: rgba(239, 68, 68, 0.3);
}

.close-icon {
  font-size: 24rpx;
  color: #64748b;
  font-weight: bold;
}

.close-btn:hover .close-icon {
  color: #ef4444;
}

.connection-details {
  margin-bottom: 32rpx;
}

.detail-item {
  display: flex;
  align-items: center;
  justify-content: space-between;
  padding: 20rpx 0;
  border-bottom: 1px solid rgba(226, 232, 240, 0.3);
}

.detail-item:last-child {
  border-bottom: none;
}

.detail-label {
  font-size: 24rpx;
  color: #64748b;
  font-weight: 500;
}

.detail-value {
  display: flex;
  align-items: center;
  gap: 12rpx;
}

.detail-value.connected .status-dot {
  background: #10b981;
  box-shadow: 0 0 0 4rpx rgba(16, 185, 129, 0.2);
}

.status-dot {
  width: 12rpx;
  height: 12rpx;
  border-radius: 50%;
  background: #ef4444;
  box-shadow: 0 0 0 4rpx rgba(239, 68, 68, 0.2);
}

.value-text {
  font-size: 24rpx;
  color: #0f172a;
  font-weight: 600;
}

.battery-indicator {
  width: 80rpx;
  height: 16rpx;
  background: rgba(226, 232, 240, 0.8);
  border-radius: 8rpx;
  overflow: hidden;
}

.battery-fill {
  height: 100%;
  background: linear-gradient(90deg, #10b981 0%, #059669 100%);
  border-radius: 8rpx;
  transition: width 0.8s cubic-bezier(0.4, 0, 0.2, 1);
}

.signal-bars {
  display: flex;
  gap: 4rpx;
  align-items: flex-end;
}

.bar {
  width: 8rpx;
  background: rgba(226, 232, 240, 0.8);
  border-radius: 2rpx;
  transition: background 0.3s ease;
}

.bar:nth-child(1) {
  height: 8rpx;
}

.bar:nth-child(2) {
  height: 12rpx;
}

.bar:nth-child(3) {
  height: 16rpx;
}

.bar:nth-child(4) {
  height: 20rpx;
}

.bar.active {
  background: #10b981;
}

.overlay-actions {
  display: flex;
  gap: 16rpx;
}

.overlay-btn {
  flex: 1;
  padding: 20rpx 24rpx;
  border-radius: 20rpx;
  text-align: center;
  transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
  cursor: pointer;
  border: 1px solid;
}

.overlay-btn.secondary {
  background: rgba(248, 250, 252, 0.8);
  border-color: rgba(226, 232, 240, 0.6);
}

.overlay-btn.secondary:hover {
  background: rgba(0, 180, 216, 0.1);
  border-color: rgba(0, 180, 216, 0.3);
}

.overlay-btn.primary {
  background: linear-gradient(135deg, #00b4d8 0%, #0096c7 100%);
  border-color: rgba(0, 180, 216, 0.3);
}

.overlay-btn.primary:hover {
  transform: translateY(-2rpx);
  box-shadow: 0 8rpx 16rpx rgba(0, 180, 216, 0.3);
}

.overlay-btn .btn-text {
  font-size: 24rpx;
  font-weight: 600;
  color: #0f172a;
}

.overlay-btn.primary .btn-text {
  color: #ffffff;
}

/* 浮动操作按钮 */
.fab-container {
  position: fixed;
  bottom: 140rpx;
  right: 40rpx;
  z-index: 100;
}

.fab {
  width: 120rpx;
  height: 120rpx;
  border-radius: 50%;
  background: linear-gradient(135deg, #8b5cf6 0%, #7c3aed 100%);
  display: flex;
  align-items: center;
  justify-content: center;
  box-shadow: 0 16rpx 32rpx rgba(139, 92, 246, 0.4);
  transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
  cursor: pointer;
  position: relative;
  overflow: hidden;
}

.fab:hover {
  transform: scale(1.1);
  box-shadow: 0 20rpx 40rpx rgba(139, 92, 246, 0.5);
}

.fab-icon {
  font-size: 48rpx;
  color: #ffffff;
  font-weight: 600;
  position: relative;
  z-index: 2;
}

.fab-pulse {
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  border-radius: 50%;
  background: rgba(255, 255, 255, 0.2);
  animation: fabPulse 3s infinite;
}

@keyframes fabPulse {
  0%, 100% {
    transform: scale(1);
    opacity: 0.7;
  }
  50% {
    transform: scale(1.2);
    opacity: 0.3;
  }
}

/* 响应式设计 */
@media (max-width: 750rpx) {
  .dashboard-container {
    padding: 20rpx;
  }
  
  .status-bar {
    padding: 24rpx;
  }
  
  .device-status {
    flex-direction: column;
    gap: 16rpx;
  }
  
  .main-data-card {
    padding: 32rpx;
  }
  
  .data-grid {
    grid-template-columns: 1fr;
    gap: 16rpx;
  }
  
  .quick-overview {
    grid-template-columns: 1fr;
    gap: 20rpx;
  }
  
  .fab-container {
    bottom: 120rpx;
    right: 30rpx;
  }
  
  .fab {
    width: 100rpx;
    height: 100rpx;
  }
  
  .fab-icon {
    font-size: 40rpx;
  }
}

/* 高级动画效果 */
@keyframes fadeInUp {
  from {
    opacity: 0;
    transform: translateY(30rpx);
  }
  to {
    opacity: 1;
    transform: translateY(0);
  }
}

@keyframes slideInRight {
  from {
    opacity: 0;
    transform: translateX(30rpx);
  }
  to {
    opacity: 1;
    transform: translateX(0);
  }
}

@keyframes scaleIn {
  from {
    opacity: 0;
    transform: scale(0.9);
  }
  to {
    opacity: 1;
    transform: scale(1);
  }
}

.status-bar {
  animation: fadeInUp 0.6s ease-out;
}

.main-data-card {
  animation: scaleIn 0.8s ease-out 0.1s both;
}

.overview-card {
  animation: slideInRight 0.6s ease-out;
}

.overview-card:nth-child(2) {
  animation-delay: 0.1s;
}

.chart-wrapper {
  animation: fadeInUp 0.8s ease-out 0.2s both;
}

.action-button {
  animation: slideInRight 0.6s ease-out;
}

.action-button:nth-child(2) {
  animation-delay: 0.1s;
}

.action-button:nth-child(3) {
  animation-delay: 0.2s;
}

.fab {
  animation: scaleIn 1s ease-out 0.5s both, fabPulse 3s infinite 1.5s;
}

/* ========== 现代化动画效果系统 ========== */

/* CSS变量定义 - 现代设计系统 */
:root {
  --animation-duration-instant: 0.1s;
  --animation-duration-fast: 0.2s;
  --animation-duration-normal: 0.4s;
  --animation-duration-slow: 0.8s;
  --animation-duration-slower: 1.2s;
  --animation-easing-standard: cubic-bezier(0.4, 0, 0.2, 1);
  --animation-easing-decelerate: cubic-bezier(0, 0, 0.2, 1);
  --animation-easing-accelerate: cubic-bezier(0.4, 0, 1, 1);
  --animation-easing-bounce: cubic-bezier(0.68, -0.55, 0.265, 1.55);
  --glow-color-safe: rgba(16, 185, 129, 0.25);
  --glow-color-warning: rgba(245, 158, 11, 0.25);
  --glow-color-danger: rgba(239, 68, 68, 0.25);
  --glow-color-primary: rgba(59, 130, 246, 0.25);
}

/* 1. 优雅的数值呼吸动画 */
@keyframes numberBreath {
  0%, 100% {
    transform: scale(1);
    text-shadow: 0 0 8px rgba(59, 130, 246, 0.3);
  }
  50% {
    transform: scale(1.02);
    text-shadow: 0 0 16px rgba(59, 130, 246, 0.5);
  }
}

.dose-rate-number {
  animation: numberBreath 4s ease-in-out infinite;
  transition: all 0.3s ease;
}

/* 2. 卡片悬停动画增强 */
.radiation-monitoring-card-redesigned,
.monitoring-section,
.calendar-filter-card {
  transition: all 0.4s cubic-bezier(0.4, 0, 0.2, 1);
}

/* 移除重复的悬停动画，已在上方定义 */

.monitoring-section:hover {
  transform: translateY(-4px);
  box-shadow: 0 16px 32px rgba(0, 0, 0, 0.12);
}

/* 3. 图标旋转动画 */
@keyframes iconRotate {
  from { transform: rotate(0deg); }
  to { transform: rotate(360deg); }
}

.radiation-icon-large svg {
  transition: transform 0.3s ease;
}

.radiation-icon-large:hover svg {
  animation: iconRotate 1s ease-in-out;
}

/* 4. 精致的状态指示器动画 */
@keyframes statusElegance {
  0%, 100% {
    transform: scale(1);
    opacity: 0.9;
    box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
  }
  50% {
    transform: scale(1.05);
    opacity: 1;
    box-shadow: 0 4px 16px rgba(0, 0, 0, 0.15);
  }
}

.status-badge {
  animation: statusElegance 4s ease-in-out infinite;
  transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
}

/* 5. 现代光环扩散动画 */
@keyframes modernAura {
  0% {
    transform: scale(0.8);
    opacity: 0.8;
    filter: blur(0px);
  }
  50% {
    transform: scale(1.2);
    opacity: 0.4;
    filter: blur(1px);
  }
  100% {
    transform: scale(1.8);
    opacity: 0;
    filter: blur(2px);
  }
}

.connection-status.connected::after {
  content: '';
  position: absolute;
  top: 50%;
  left: 50%;
  width: 16px;
  height: 16px;
  background: linear-gradient(135deg, #22c55e 0%, #16a34a 100%);
  border-radius: 50%;
  transform: translate(-50%, -50%);
  animation: modernAura 3s ease-out infinite;
}

/* 6. 矩阵格子优雅提示动画 */
@keyframes matrixGlow {
  0%, 100% {
    opacity: 1;
    box-shadow: 0 0 8px rgba(0, 0, 0, 0.1);
  }
  50% {
    opacity: 0.85;
    box-shadow: 0 0 16px rgba(239, 68, 68, 0.3);
  }
}

.matrix-cell.danger {
  animation: matrixGlow 4s ease-in-out infinite;
  transition: all 0.3s ease;
}

.matrix-cell.warning {
  animation: matrixGlow 6s ease-in-out infinite;
  transition: all 0.3s ease;
}

/* 7. 按钮点击波纹效果 */
@keyframes clickRipple {
  0% {
    transform: scale(0);
    opacity: 1;
  }
  100% {
    transform: scale(4);
    opacity: 0;
  }
}

.preset-btn {
  position: relative;
  overflow: hidden;
}

.preset-btn::before {
  content: '';
  position: absolute;
  top: 50%;
  left: 50%;
  width: 0;
  height: 0;
  border-radius: 50%;
  background: rgba(255, 255, 255, 0.5);
  transform: translate(-50%, -50%);
  transition: width 0.6s, height 0.6s;
}

.preset-btn:active::before {
  width: 300px;
  height: 300px;
}

/* 8. 加载骨架动画 */
@keyframes shimmer {
  0% {
    background-position: -200px 0;
  }
  100% {
    background-position: calc(200px + 100%) 0;
  }
}

.loading-skeleton {
  background: linear-gradient(90deg, #f0f0f0 25%, #e0e0e0 50%, #f0f0f0 75%);
  background-size: 200px 100%;
  animation: shimmer 1.5s infinite;
}

/* 9. 图表数据点动画 */
@keyframes dataPointPulse {
  0%, 100% {
    transform: scale(1);
    opacity: 0.8;
  }
  50% {
    transform: scale(1.3);
    opacity: 1;
  }
}

.chart-data-point {
  animation: dataPointPulse 2s ease-in-out infinite;
}

/* 10. 通知徽章优雅提示 */
@keyframes badgeGlow {
  0%, 100% {
    transform: scale(1);
    box-shadow: 0 4px 12px rgba(239, 68, 68, 0.4);
  }
  50% {
    transform: scale(1.05);
    box-shadow: 0 6px 16px rgba(239, 68, 68, 0.6);
  }
}

.notification-badge {
  animation: badgeGlow 3s ease-in-out infinite;
}

/* 11. 渐变背景流动 */
@keyframes gradientFlow {
  0% {
    background-position: 0% 50%;
  }
  50% {
    background-position: 100% 50%;
  }
  100% {
    background-position: 0% 50%;
  }
}

.header-gradient {
  background: linear-gradient(-45deg,
    rgba(255, 255, 255, 0.95),
    rgba(248, 250, 252, 0.9),
    rgba(240, 245, 251, 0.85),
    rgba(248, 250, 252, 0.9));
  background-size: 400% 400%;
  animation: gradientFlow 8s ease infinite;
}

/* 12. 文字打字机效果 */
@keyframes typewriter {
  from { width: 0; }
  to { width: 100%; }
}

.typewriter-text {
  overflow: hidden;
  border-right: 2px solid #3b82f6;
  white-space: nowrap;
  animation: typewriter 3s steps(40, end), blink-caret 0.75s step-end infinite;
}

@keyframes blink-caret {
  from, to { border-color: transparent; }
  50% { border-color: #3b82f6; }
}

/* 13. 3D翻转效果 */
@keyframes flip3D {
  0% { transform: perspective(400px) rotateY(0); }
  100% { transform: perspective(400px) rotateY(360deg); }
}

.flip-card {
  transition: transform 0.6s;
  transform-style: preserve-3d;
}

.flip-card:hover {
  animation: flip3D 1s ease-in-out;
}

/* 14. 粒子漂浮效果 */
@keyframes float {
  0%, 100% { transform: translateY(0px); }
  50% { transform: translateY(-20px); }
}

.floating-particle {
  animation: float 6s ease-in-out infinite;
}

.floating-particle:nth-child(2) { animation-delay: 2s; }
.floating-particle:nth-child(3) { animation-delay: 4s; }

/* 15. 进度条填充动画 */
@keyframes progressFill {
  from { width: 0%; }
  to { width: var(--progress-width, 100%); }
}

.progress-bar {
  animation: progressFill 2s ease-out;
}

/* 16. 卡片入场动画变体 */
@keyframes slideInFromLeft {
  from {
    opacity: 0;
    transform: translateX(-100px);
  }
  to {
    opacity: 1;
    transform: translateX(0);
  }
}

@keyframes slideInFromRight {
  from {
    opacity: 0;
    transform: translateX(100px);
  }
  to {
    opacity: 1;
    transform: translateX(0);
  }
}

@keyframes zoomIn {
  from {
    opacity: 0;
    transform: scale(0.3);
  }
  to {
    opacity: 1;
    transform: scale(1);
  }
}

/* 应用新的入场动画 */
.modern-header {
  animation: slideInFromLeft 0.8s ease-out;
}

.time-filter-container {
  animation: slideInFromRight 0.8s ease-out 0.2s both;
}

.radiation-matrix {
  animation: zoomIn 0.8s ease-out 0.4s both;
}

.matrix-legend {
  animation: fadeInUp 0.6s ease-out 0.6s both;
}

/* 17. 鼠标悬停时的微交互 */
.mode-card,
.device-card {
  transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
}

.mode-card:hover,
.device-card:hover {
  transform: translateY(-5px) scale(1.03);
  box-shadow: 0 15px 30px rgba(0, 0, 0, 0.15);
}

.mode-card:active,
.device-card:active {
  transform: translateY(-2px) scale(1.01);
}

/* 18. 数据更新闪烁提示 */
@keyframes dataUpdate {
  0% { background-color: rgba(59, 130, 246, 0.1); }
  50% { background-color: rgba(59, 130, 246, 0.3); }
  100% { background-color: transparent; }
}

.data-updated {
  animation: dataUpdate 1s ease-out;
}

/* 19. 错误状态抖动 */
@keyframes shake {
  0%, 100% { transform: translateX(0); }
  10%, 30%, 50%, 70%, 90% { transform: translateX(-5px); }
  20%, 40%, 60%, 80% { transform: translateX(5px); }
}

.error-shake {
  animation: shake 0.5s ease-in-out;
}

/* 20. 成功状态弹跳 */
@keyframes successBounce {
  0%, 20%, 50%, 80%, 100% { transform: translateY(0); }
  40% { transform: translateY(-10px); }
  60% { transform: translateY(-5px); }
}

.success-bounce {
  animation: successBounce 1s ease-out;
}

/* ========== 高级视觉效果 ========== */

/* 21. 磁场扫描效果 */
@keyframes magneticScan {
  0% {
    transform: translateX(-100%);
    opacity: 0;
  }
  50% {
    opacity: 1;
  }
  100% {
    transform: translateX(100%);
    opacity: 0;
  }
}

.radiation-monitoring-card-redesigned::after {
  content: '';
  position: absolute;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  background: linear-gradient(90deg,
    transparent,
    rgba(59, 130, 246, 0.1),
    rgba(59, 130, 246, 0.2),
    rgba(59, 130, 246, 0.1),
    transparent);
  animation: magneticScan 4s infinite;
  pointer-events: none;
  border-radius: 20px;
}

/* 22. 数据流动效果 */
@keyframes dataFlow {
  0% {
    background-position: 0% 0%;
  }
  100% {
    background-position: 100% 100%;
  }
}

.charts-container-optimized::before {
  content: '';
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background: linear-gradient(45deg,
    transparent 30%,
    rgba(34, 197, 94, 0.05) 50%,
    transparent 70%);
  background-size: 200% 200%;
  animation: dataFlow 3s linear infinite;
  pointer-events: none;
  border-radius: 24px;
}

/* 23. 能量脉冲环 */
@keyframes energyPulse {
  0% {
    transform: scale(0.8);
    opacity: 1;
  }
  50% {
    transform: scale(1.2);
    opacity: 0.5;
  }
  100% {
    transform: scale(1.5);
    opacity: 0;
  }
}

.radiation-icon-large::before {
  content: '';
  position: absolute;
  top: 50%;
  left: 50%;
  width: 60px;
  height: 60px;
  border: 2px solid currentColor;
  border-radius: 50%;
  transform: translate(-50%, -50%);
  animation: energyPulse 2s infinite;
  opacity: 0.3;
}

/* 24. 全息投影效果 */
@keyframes hologram {
  0%, 100% {
    text-shadow:
      0 0 5px currentColor,
      0 0 10px currentColor,
      0 0 15px currentColor;
  }
  50% {
    text-shadow:
      0 0 2px currentColor,
      0 0 5px currentColor,
      0 0 8px currentColor;
  }
}

.dose-rate-number.danger {
  animation: hologram 1.5s infinite, numberJump 2s ease-in-out infinite;
}

/* 25. 矩阵雨效果 */
@keyframes matrixRain {
  0% {
    transform: translateY(-100%);
    opacity: 0;
  }
  10% {
    opacity: 1;
  }
  90% {
    opacity: 1;
  }
  100% {
    transform: translateY(100vh);
    opacity: 0;
  }
}

.matrix-rain-drop {
  position: fixed;
  color: #00ff00;
  font-family: 'Courier New', monospace;
  font-size: 12px;
  animation: matrixRain 3s linear infinite;
  pointer-events: none;
  z-index: 1;
}

/* 26. 量子闪烁效果 */
@keyframes quantumFlicker {
  0%, 100% { opacity: 1; }
  25% { opacity: 0.3; }
  50% { opacity: 0.8; }
  75% { opacity: 0.1; }
}

.matrix-cell.no-data {
  animation: quantumFlicker 4s infinite;
}

/* 27. 电磁干扰效果 */
@keyframes electromagnetic {
  0% { filter: hue-rotate(0deg); }
  25% { filter: hue-rotate(90deg) saturate(1.5); }
  50% { filter: hue-rotate(180deg) saturate(0.8); }
  75% { filter: hue-rotate(270deg) saturate(1.2); }
  100% { filter: hue-rotate(360deg); }
}

.radiation-monitoring-card-redesigned.danger {
  animation: electromagnetic 2s infinite;
}

/* 28. 时空扭曲效果 */
@keyframes spaceWarp {
  0%, 100% {
    transform: perspective(1000px) rotateX(0deg) rotateY(0deg);
  }
  25% {
    transform: perspective(1000px) rotateX(5deg) rotateY(5deg);
  }
  50% {
    transform: perspective(1000px) rotateX(0deg) rotateY(10deg);
  }
  75% {
    transform: perspective(1000px) rotateX(-5deg) rotateY(5deg);
  }
}

.time-filter-container:hover {
  animation: spaceWarp 2s ease-in-out;
}

/* 29. 神经网络连接线 */
@keyframes neuralConnection {
  0% {
    stroke-dashoffset: 100;
    opacity: 0;
  }
  50% {
    opacity: 1;
  }
  100% {
    stroke-dashoffset: 0;
    opacity: 0.7;
  }
}

.neural-line {
  stroke: #3b82f6;
  stroke-width: 2;
  stroke-dasharray: 10;
  animation: neuralConnection 3s infinite;
  opacity: 0.3;
}

/* 30. 反重力悬浮 */
@keyframes antiGravity {
  0%, 100% {
    transform: translateY(0px) rotate(0deg);
  }
  25% {
    transform: translateY(-10px) rotate(1deg);
  }
  50% {
    transform: translateY(-5px) rotate(0deg);
  }
  75% {
    transform: translateY(-15px) rotate(-1deg);
  }
}

.floating-element {
  animation: antiGravity 4s ease-in-out infinite;
}

/* 31. 能量充电效果 */
@keyframes energyCharge {
  0% {
    background: linear-gradient(90deg, transparent 0%, transparent 100%);
  }
  50% {
    background: linear-gradient(90deg, transparent 0%, rgba(34, 197, 94, 0.3) 50%, transparent 100%);
  }
  100% {
    background: linear-gradient(90deg, transparent 0%, rgba(34, 197, 94, 0.6) 100%);
  }
}

.charging-bar {
  animation: energyCharge 2s ease-in-out infinite;
}

/* 32. 响应式动画调整 */
@media (prefers-reduced-motion: reduce) {
  * {
    animation-duration: 0.01ms !important;
    animation-iteration-count: 1 !important;
    transition-duration: 0.01ms !important;
  }
}

/* 33. 性能优化的动画 */
.gpu-accelerated {
  will-change: transform, opacity;
  transform: translateZ(0);
  backface-visibility: hidden;
}

/* 34. 交互式动画触发器 */
.interactive-trigger:hover .animated-element {
  animation-play-state: running;
}

.interactive-trigger .animated-element {
  animation-play-state: paused;
}

/* ========== 新增现代微交互动画 ========== */

/* 35. 磁性悬停效果 */
@keyframes magneticHover {
  0% { transform: translate(0, 0) scale(1); }
  100% { transform: translate(var(--mouse-x, 0), var(--mouse-y, 0)) scale(1.05); }
}

.magnetic-element {
  transition: all var(--animation-duration-fast) var(--animation-easing-standard);
  cursor: pointer;
}

.magnetic-element:hover {
  animation: magneticHover var(--animation-duration-normal) var(--animation-easing-decelerate) forwards;
}

/* 36. 液体按钮效果 */
@keyframes liquidButton {
  0% { border-radius: 24px; }
  50% { border-radius: 32px 16px 32px 16px; }
  100% { border-radius: 24px; }
}

.liquid-button {
  animation: liquidButton 3s ease-in-out infinite;
  transition: all var(--animation-duration-normal) var(--animation-easing-standard);
}

.liquid-button:hover {
  animation-play-state: paused;
  border-radius: 16px;
}

/* 37. 数据流光效果 */
@keyframes dataStream {
  0% {
    background-position: -200% 0;
    opacity: 0;
  }
  50% {
    opacity: 1;
  }
  100% {
    background-position: 200% 0;
    opacity: 0;
  }
}

.data-stream::before {
  content: '';
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background: linear-gradient(
    90deg,
    transparent 0%,
    rgba(59, 130, 246, 0.3) 50%,
    transparent 100%
  );
  animation: dataStream 2s ease-in-out infinite;
  pointer-events: none;
}

/* 38. 智能卡片阴影 */
@keyframes smartShadow {
  0%, 100% {
    box-shadow:
      0 4px 8px rgba(0, 0, 0, 0.06),
      0 1px 3px rgba(0, 0, 0, 0.04);
  }
  50% {
    box-shadow:
      0 8px 16px rgba(0, 0, 0, 0.08),
      0 2px 6px rgba(0, 0, 0, 0.06),
      0 0 0 1px rgba(59, 130, 246, 0.1);
  }
}

.smart-shadow {
  animation: smartShadow 4s ease-in-out infinite;
}

/* 39. 呼吸光晕 */
@keyframes breathingGlow {
  0%, 100% {
    filter: drop-shadow(0 0 8px var(--glow-color-primary));
    opacity: 0.8;
  }
  50% {
    filter: drop-shadow(0 0 16px var(--glow-color-primary));
    opacity: 1;
  }
}

.breathing-glow {
  animation: breathingGlow 3s ease-in-out infinite;
}

/* 40. 现代加载动画 */
@keyframes modernLoader {
  0% {
    transform: rotate(0deg) scale(1);
    border-radius: 50%;
  }
  25% {
    transform: rotate(90deg) scale(1.1);
    border-radius: 30%;
  }
  50% {
    transform: rotate(180deg) scale(1);
    border-radius: 50%;
  }
  75% {
    transform: rotate(270deg) scale(0.9);
    border-radius: 30%;
  }
  100% {
    transform: rotate(360deg) scale(1);
    border-radius: 50%;
  }
}

.modern-loader {
  animation: modernLoader 1.5s ease-in-out infinite;
}
</style>