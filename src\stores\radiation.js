import { ref, reactive, computed } from 'vue'

// 辐射监测数据存储
const radiationState = reactive({
  current: {
    doseRate: 0.125,
    cps: 45,
    timestamp: new Date(),
    status: 'normal'
  },
  history: [],
  isConnected: false,
  lastUpdate: null
})

// 设备状态
const deviceState = reactive({
  isConnected: false,
  batteryLevel: 85,
  signalStrength: 4,
  deviceId: 'RD-001',
  firmwareVersion: '1.2.3'
})

// 通知状态
const notificationState = reactive({
  notifications: [
    {
      id: 1,
      type: 'normal',
      title: '系统启动',
      message: '辐射监测系统已成功启动',
      timestamp: new Date(Date.now() - 1000 * 60 * 5),
      read: false
    },
    {
      id: 2,
      type: 'warning',
      title: '数值异常',
      message: '检测到辐射值轻微上升，请注意监控',
      timestamp: new Date(Date.now() - 1000 * 60 * 30),
      read: false
    },
    {
      id: 3,
      type: 'normal',
      title: '设备连接',
      message: '监测设备已成功连接',
      timestamp: new Date(Date.now() - 1000 * 60 * 60),
      read: true
    }
  ],
  unreadCount: 0
})

// 计算属性
const currentTrend = computed(() => {
  if (radiationState.history.length < 2) {
    return { text: '稳定', icon: '📊', class: 'stable' }
  }
  
  const recent = radiationState.history.slice(-5)
  const avg = recent.reduce((sum, item) => sum + item.doseRate, 0) / recent.length
  const current = radiationState.current.doseRate
  
  if (current > avg * 1.1) {
    return { text: '上升', icon: '📈', class: 'rising' }
  } else if (current < avg * 0.9) {
    return { text: '下降', icon: '📉', class: 'falling' }
  } else {
    return { text: '稳定', icon: '📊', class: 'stable' }
  }
})

const radiationLevel = computed(() => {
  const doseRate = radiationState.current.doseRate
  if (doseRate < 0.5) return 'normal'
  if (doseRate < 1.0) return 'elevated'
  if (doseRate < 2.0) return 'high'
  return 'critical'
})

const radiationStatusText = computed(() => {
  const level = radiationLevel.value
  switch (level) {
    case 'normal': return '正常水平'
    case 'elevated': return '轻微升高'
    case 'high': return '偏高'
    case 'critical': return '危险水平'
    default: return '未知'
  }
})

// 更新未读通知数量
const updateUnreadCount = () => {
  notificationState.unreadCount = notificationState.notifications.filter(n => !n.read).length
}

// 初始化未读数量
updateUnreadCount()

// 方法
const updateRadiationData = (newData) => {
  radiationState.current = { ...radiationState.current, ...newData, timestamp: new Date() }
  radiationState.history.push({ ...radiationState.current })
  
  // 保持历史记录在合理范围内
  if (radiationState.history.length > 1000) {
    radiationState.history = radiationState.history.slice(-500)
  }
  
  radiationState.lastUpdate = new Date()
}

const addNotification = (notification) => {
  const newNotification = {
    id: Date.now(),
    timestamp: new Date(),
    read: false,
    ...notification
  }
  notificationState.notifications.unshift(newNotification)
  updateUnreadCount()
}

const markNotificationAsRead = (id) => {
  const notification = notificationState.notifications.find(n => n.id === id)
  if (notification) {
    notification.read = true
    updateUnreadCount()
  }
}

const clearAllNotifications = () => {
  notificationState.notifications = []
  notificationState.unreadCount = 0
}

const setDeviceConnection = (connected) => {
  deviceState.isConnected = connected
  radiationState.isConnected = connected
}

// 导出store函数
export const useRadiationStore = () => {
  return {
    // 状态
    radiationState,
    deviceState,
    notificationState,
    
    // 计算属性
    currentTrend,
    radiationLevel,
    radiationStatusText,
    
    // 方法
    updateRadiationData,
    addNotification,
    markNotificationAsRead,
    clearAllNotifications,
    setDeviceConnection,
    updateUnreadCount
  }
}

// 默认导出
export default useRadiationStore
