/**
 * 响应式样式管理器
 * 统一管理应用的响应式样式和适配逻辑
 */

import { getDeviceInfo, pxToRpx, CommonSizes } from './adaptiveUtils.js';

class ResponsiveManager {
  constructor() {
    this.deviceInfo = null;
    this.isInitialized = false;
    this.listeners = [];
    this.currentBreakpoint = 'md'; // 默认中等屏幕
  }

  /**
   * 初始化响应式管理器
   */
  async init() {
    if (this.isInitialized) return;
    
    try {
      this.deviceInfo = await getDeviceInfo();
      this.currentBreakpoint = this.getBreakpoint();
      this.isInitialized = true;
      
      // 通知所有监听器
      this.notifyListeners();
      
      console.log('ResponsiveManager initialized:', {
        deviceInfo: this.deviceInfo,
        breakpoint: this.currentBreakpoint
      });
    } catch (error) {
      console.error('Failed to initialize ResponsiveManager:', error);
    }
  }

  /**
   * 获取当前设备的断点
   */
  getBreakpoint() {
    if (!this.deviceInfo) return 'md';
    
    const width = this.deviceInfo.windowWidth;
    
    if (width < 320) return 'xs';
    if (width < 375) return 'sm';
    if (width < 390) return 'md';
    if (width < 414) return 'lg';
    if (width < 768) return 'xl';
    return 'xxl';
  }

  /**
   * 添加响应式变化监听器
   */
  addListener(callback) {
    this.listeners.push(callback);
    
    // 如果已经初始化，立即调用回调
    if (this.isInitialized) {
      callback(this.deviceInfo, this.currentBreakpoint);
    }
  }

  /**
   * 移除监听器
   */
  removeListener(callback) {
    const index = this.listeners.indexOf(callback);
    if (index > -1) {
      this.listeners.splice(index, 1);
    }
  }

  /**
   * 通知所有监听器
   */
  notifyListeners() {
    this.listeners.forEach(callback => {
      try {
        callback(this.deviceInfo, this.currentBreakpoint);
      } catch (error) {
        console.error('Error in responsive listener:', error);
      }
    });
  }

  /**
   * 获取响应式样式
   */
  getResponsiveStyles(baseStyles) {
    if (!this.deviceInfo) return baseStyles;
    
    const styles = { ...baseStyles };
    const breakpoint = this.currentBreakpoint;
    
    // 根据断点调整样式
    switch (breakpoint) {
      case 'xs':
        return this.adjustStylesForXS(styles);
      case 'sm':
        return this.adjustStylesForSM(styles);
      case 'md':
        return styles; // 基准尺寸
      case 'lg':
        return this.adjustStylesForLG(styles);
      case 'xl':
        return this.adjustStylesForXL(styles);
      case 'xxl':
        return this.adjustStylesForXXL(styles);
      default:
        return styles;
    }
  }

  /**
   * 超小屏样式调整
   */
  adjustStylesForXS(styles) {
    return {
      ...styles,
      fontSize: this.scaleValue(styles.fontSize, 0.85),
      padding: this.scaleValue(styles.padding, 0.8),
      margin: this.scaleValue(styles.margin, 0.8),
      borderRadius: this.scaleValue(styles.borderRadius, 0.9)
    };
  }

  /**
   * 小屏样式调整
   */
  adjustStylesForSM(styles) {
    return {
      ...styles,
      fontSize: this.scaleValue(styles.fontSize, 0.9),
      padding: this.scaleValue(styles.padding, 0.9),
      margin: this.scaleValue(styles.margin, 0.9),
      borderRadius: this.scaleValue(styles.borderRadius, 0.95)
    };
  }

  /**
   * 大屏样式调整
   */
  adjustStylesForLG(styles) {
    return {
      ...styles,
      fontSize: this.scaleValue(styles.fontSize, 1.1),
      padding: this.scaleValue(styles.padding, 1.1),
      margin: this.scaleValue(styles.margin, 1.1),
      borderRadius: this.scaleValue(styles.borderRadius, 1.05)
    };
  }

  /**
   * 超大屏样式调整
   */
  adjustStylesForXL(styles) {
    return {
      ...styles,
      fontSize: this.scaleValue(styles.fontSize, 1.15),
      padding: this.scaleValue(styles.padding, 1.15),
      margin: this.scaleValue(styles.margin, 1.15),
      borderRadius: this.scaleValue(styles.borderRadius, 1.1)
    };
  }

  /**
   * 平板样式调整
   */
  adjustStylesForXXL(styles) {
    return {
      ...styles,
      fontSize: this.scaleValue(styles.fontSize, 1.2),
      padding: this.scaleValue(styles.padding, 1.2),
      margin: this.scaleValue(styles.margin, 1.2),
      borderRadius: this.scaleValue(styles.borderRadius, 1.15)
    };
  }

  /**
   * 缩放数值
   */
  scaleValue(value, scale) {
    if (typeof value === 'number') {
      return Math.round(value * scale);
    }
    if (typeof value === 'string' && value.includes('rpx')) {
      const num = parseFloat(value);
      return `${Math.round(num * scale)}rpx`;
    }
    return value;
  }

  /**
   * 获取响应式字体大小
   */
  getResponsiveFontSize(baseFontSize) {
    const breakpoint = this.currentBreakpoint;
    const scales = {
      xs: 0.85,
      sm: 0.9,
      md: 1,
      lg: 1.1,
      xl: 1.15,
      xxl: 1.2
    };
    
    const scale = scales[breakpoint] || 1;
    return Math.round(baseFontSize * scale);
  }

  /**
   * 获取响应式间距
   */
  getResponsiveSpacing(baseSpacing) {
    const breakpoint = this.currentBreakpoint;
    const scales = {
      xs: 0.8,
      sm: 0.9,
      md: 1,
      lg: 1.1,
      xl: 1.15,
      xxl: 1.2
    };
    
    const scale = scales[breakpoint] || 1;
    return Math.round(baseSpacing * scale);
  }

  /**
   * 获取当前设备信息
   */
  getDeviceInfo() {
    return this.deviceInfo;
  }

  /**
   * 获取当前断点
   */
  getCurrentBreakpoint() {
    return this.currentBreakpoint;
  }

  /**
   * 判断是否为小屏设备
   */
  isSmallScreen() {
    return ['xs', 'sm'].includes(this.currentBreakpoint);
  }

  /**
   * 判断是否为大屏设备
   */
  isLargeScreen() {
    return ['xl', 'xxl'].includes(this.currentBreakpoint);
  }

  /**
   * 获取网格列数
   */
  getGridColumns(defaultColumns = 2) {
    const breakpoint = this.currentBreakpoint;
    const columnMap = {
      xs: Math.max(1, defaultColumns - 1),
      sm: Math.max(1, defaultColumns - 1),
      md: defaultColumns,
      lg: defaultColumns,
      xl: defaultColumns + 1,
      xxl: defaultColumns + 2
    };
    
    return columnMap[breakpoint] || defaultColumns;
  }

  /**
   * 创建响应式CSS类名
   */
  createResponsiveClass(baseClass) {
    return `${baseClass} ${baseClass}--${this.currentBreakpoint}`;
  }
}

// 创建全局实例
const responsiveManager = new ResponsiveManager();

// 导出实例和工具函数
export default responsiveManager;

export {
  ResponsiveManager,
  responsiveManager,
  pxToRpx,
  CommonSizes
};

// 自动初始化（在应用启动时）
export const initResponsiveManager = async () => {
  await responsiveManager.init();
  return responsiveManager;
};
